import { createContext, useContext, useEffect, useState, useRef } from "react";
import { useAuth } from "./AuthContext";
import { captureError, trackEvent } from "../services/postHogService";
import { useGetCreditsBalanceQuery, CreditsBalance, useGetSubscriptionPlansQuery, SubscriptionPlansResponse } from "../store/api/apiSlice";
import { useLocation } from "react-router";
import { useProModePayment } from "@/hooks/useProModePayment";

interface CreditsContextType {
  credits: number;
  loading: boolean;
  error: string | null;
  tier: "free" | "pro" | "starter" | "standard" | "pro_mode";
  pro_mode: boolean;
  refreshCredits: () => Promise<void>;
  creditResponse: CreditsBalance | null;
  subscriptionPlansResponse: SubscriptionPlansResponse | null;
  upgradeDetails: {
    id: string,
    amount: number,
    name:string
  } | null;
  currentSubscriptionDetail: {
    id: string,
    amount: number,
    name:string
  } | null;
  getUpgradeTierName: () => string;
}

const CreditsContext = createContext<CreditsContextType>({
  credits: 0,
  loading: true,
  error: null,
  tier: "free",
  pro_mode: false,
  refreshCredits: async () => {},
  creditResponse: null,
  subscriptionPlansResponse: null,
  upgradeDetails: null,
  currentSubscriptionDetail: null,
  getUpgradeTierName: () => "Starter"
});

export function CreditsProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();
  const location = useLocation();
  const [upgradeDetails, setUpgradeDetails] = useState<{
    id: string,
    amount:number,
    name:string
  } | null>({
    id: "5d98824c-26c1-4dc4-822a-007ad1d5e684",
    amount: 10,
    name: "Emergent Starter"
  });
  const [tier, setTier] = useState<"free"| "pro" |"starter" | "standard" | "pro_mode">("free");
  const [proMode, setProMode] = useState<boolean>(false);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);
  const [currentSubscriptionDetail, setCurrentSubscriptionDetail] = useState<{
    id: string,
    amount:number,
    name:string,
  } | null>(null);
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef<number>(0);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const MAX_RETRY_ATTEMPTS = 3;

  // Use RTK Query for credits balance with automatic retry
  const {
    data: creditResponse,
    error: queryError,
    isLoading: loading,
    refetch: refetchCredits
  } = useGetCreditsBalanceQuery(undefined, {
    skip: !user?.user_metadata?.invite_code, // Skip if user is not verified
    pollingInterval: 0, // Disable automatic polling, we'll handle manual refresh
  });

  // Use RTK Query for subscription plans
  const {
    data: subscriptionPlansResponse,
    error: subscriptionPlansError,
    isLoading: subscriptionPlansLoading,
    refetch: refetchSubscriptionPlans
  } = useGetSubscriptionPlansQuery(undefined, {
    skip: !user?.user_metadata?.invite_code, // Skip if user is not verified
    pollingInterval: 0, // Disable automatic polling
  });

  const {paidBefore30July} = useProModePayment(); 

  // Derive credits and error from RTK Query state
  const credits = creditResponse?.ecu_balance || 0;
  // Show loading state during retry attempts, only show error after all retry attempts are exhausted
  const isRetrying = queryError && retryCountRef.current < MAX_RETRY_ATTEMPTS;
  const effectiveLoading = loading || !!isRetrying;
  const error = (queryError && retryCountRef.current >= MAX_RETRY_ATTEMPTS) ?
    (typeof queryError === 'object' && 'data' in queryError ?
      (queryError.data as any)?.message || 'Failed to fetch credits balance' :
      'Failed to fetch credits balance') :
    null;

  const getTierInformation = (subscriptionPlans?: SubscriptionPlansResponse, creditData?: CreditsBalance): "free" | "pro" | "starter" | "standard" | "pro_mode" => {
    // First try to get tier from subscription plans API (primary source)
    if (subscriptionPlans?.current_subscription) {
      const tierName = subscriptionPlans.current_subscription.tier_name.toLowerCase();

      // Check for "Emergent Pro" specifically for pro_mode
      if (tierName.includes("emergent pro")) {
        return "pro_mode";
      } else if (tierName.includes("standard")) {
        return "standard";
      } else if (tierName.includes("starter")) {
        return "starter";
      } else if (tierName.includes("pro")) {
        return "pro";
      }
    }

    // Fallback to credits balance API (existing logic)
    const data = creditData || creditResponse;

    if (!data) {
      return "free";
    }

    if (!data.subscription || !data.subscription.id) {
      return "free";
    }

    const name = data.subscription.name.toLowerCase();

    // Check for specific tier names in order of specificity (most specific first)
    if (name.includes("emergent pro")) {
      return "pro_mode";
    } else if (name.includes("standard")) {
      return "standard";
    } else if (name.includes("starter")) {
      return "starter";
    } else if (name.includes("pro")) {
      return "pro";
    } else {
      return "free";
    }
  }

  const getProModeStatus = (subscriptionPlans?: SubscriptionPlansResponse, creditData?: CreditsBalance): boolean => {
    // First check subscription plans API for "Emergent Pro" tier
    if (subscriptionPlans?.current_subscription) {
      const tierName = subscriptionPlans.current_subscription.tier_name.toLowerCase();
      return tierName.includes("emergent pro");
    }

    // Fallback to credits balance API
    const data = creditData || creditResponse;
    if (data?.subscription?.name) {
      const name = data.subscription.name.toLowerCase();
      return name.includes("emergent pro");
    }

    return false;
  }

  const getUpgradeTierName = (): string => {
    if (!upgradeDetails) return "Starter";

    const name = upgradeDetails.name.replaceAll(" ", "").toLowerCase();

    if (name.includes("standard")) {
      return "Standard";
    } else if (name.includes("starter")) {
      return "Starter";
    } else if (name.includes("pro")) {
      return "Pro";
    } else {
      return "Starter";
    }
  };



  // Refresh credits and subscription plans when URL changes (e.g., returning from payment flow)
  useEffect(() => {
    if (user?.user_metadata?.invite_code) {
      // Trigger refetch when URL changes, but only if user is verified
      refetchCredits();
      refetchSubscriptionPlans();
    }
  }, [location.pathname, location.search, user?.user_metadata?.invite_code, refetchCredits, refetchSubscriptionPlans]);

  // Handle response data and update state when subscription plans or credit response changes
  useEffect(() => {
    // Determine tier using subscription plans as primary source, credits as fallback
    const tierName = getTierInformation(subscriptionPlansResponse, creditResponse);
    setTier(tierName);

    // Determine pro mode status
    const proModeStatus = getProModeStatus(subscriptionPlansResponse, creditResponse);
    setProMode(proModeStatus);

    // Update current subscription details - prefer subscription plans API
    if (subscriptionPlansResponse?.current_subscription) {
      setCurrentSubscriptionDetail({
        id: subscriptionPlansResponse.current_subscription.id,
        name: subscriptionPlansResponse.current_subscription.tier_name,
        amount: subscriptionPlansResponse.current_subscription.amount
      });
    } else if (creditResponse?.subscription && creditResponse.subscription.id) {
      // Fallback to credits API
      setCurrentSubscriptionDetail({
        id: creditResponse.subscription.id,
        name: creditResponse.subscription.name,
        amount: creditResponse.subscription.subscription_details?.amount || 0
      });
    } else {
      setCurrentSubscriptionDetail(null);
    }

    // Set upgrade details - prefer subscription plans API for available plans
    if (subscriptionPlansResponse?.available_plans && subscriptionPlansResponse.available_plans.length > 0) {
      // Find the first available plan that's different from current subscription
      const currentTier = subscriptionPlansResponse.current_subscription?.tier_name.toLowerCase();
      const upgradeOption = subscriptionPlansResponse.available_plans.filter(plan =>
        plan.tier_name.toLowerCase() !== "emergent pro" || paidBefore30July
      ).find(plan =>
        plan.tier_name.toLowerCase() !== currentTier
      );

      if (upgradeOption) {
        setUpgradeDetails({
          id: upgradeOption.id,
          name: upgradeOption.tier_name,
          amount: upgradeOption.amount
        });
      }
    } else if (creditResponse?.subscription?.subscription_details) {
      // Fallback to credits API
      setUpgradeDetails({
        id: creditResponse.subscription.subscription_details.id,
        name: creditResponse.subscription.name,
        amount: creditResponse.subscription.subscription_details.amount
      });
    }
  }, [subscriptionPlansResponse, creditResponse]);

  // Handle errors and implement retry logic
  useEffect(() => {
    if (queryError && user) {
      console.error(`Failed to fetch credits (attempt ${retryCountRef.current + 1}/${MAX_RETRY_ATTEMPTS}):`, queryError);

      // Retry if we haven't exceeded max attempts
      if (retryCountRef.current < MAX_RETRY_ATTEMPTS) {
        retryCountRef.current += 1;

        // Clear any existing retry timeout
        if (retryTimeoutRef.current) {
          clearTimeout(retryTimeoutRef.current);
        }

        // Retry with exponential backoff: 1s, 2s, 4s
        const retryDelay = Math.pow(2, retryCountRef.current - 1) * 1000;
        console.log(`Retrying credits fetch in ${retryDelay}ms (attempt ${retryCountRef.current}/${MAX_RETRY_ATTEMPTS})`);

        retryTimeoutRef.current = setTimeout(() => {
          refetchCredits();
          retryTimeoutRef.current = null;
        }, retryDelay);
      } else {
        // All retries exhausted, track the error
        console.error("All retry attempts exhausted for credits fetch");

        trackEvent('credits_fetch_failed', {
          userId: user.id,
          errorType: 'api_error',
          errorMessage: typeof queryError === 'object' && 'data' in queryError ?
            (queryError.data as any)?.message || 'Failed to fetch credits balance' :
            'Failed to fetch credits balance',
          hasInviteCode: !!user.user_metadata?.invite_code,
          retryAttempts: retryCountRef.current
        });

        if (typeof queryError === 'object' && 'data' in queryError) {
          captureError(`Credits API returned error after ${retryCountRef.current} attempts`, {
            userId: user.id,
            errorType: 'api_response_error',
            apiResponse: queryError.data,
            context: 'credits_fetch',
            retryAttempts: retryCountRef.current
          });
        }
      }
    } else if (!queryError) {
      // Reset retry count when error is cleared (successful response)
      retryCountRef.current = 0;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
    }
  }, [queryError, user, refetchCredits]);

  // Cleanup timeouts on unmount
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  const refreshCredits = async () => {
    // If a refresh is already in progress, ignore this call
    if (isRefreshing) {
      console.log("Credits refresh already in progress, ignoring this call");
      return;
    }

    if (!user?.user_metadata.invite_code) {
      console.log("User is not verified, not refreshing credits");
      return;
    }

    // Reset retry counter for manual refresh
    retryCountRef.current = 0;

    // Clear any existing retry timeout
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }

    // Set the refreshing state to true
    setIsRefreshing(true);

    try {
      // Perform the actual refresh using RTK Query refetch for both APIs
      await Promise.all([
        refetchCredits(),
        refetchSubscriptionPlans()
      ]);
    } finally {
      // Clear any existing timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      // Set a timeout to clear the refreshing state after 5 seconds
      refreshTimeoutRef.current = setTimeout(() => {
        setIsRefreshing(false);
        refreshTimeoutRef.current = null;
      }, 5000); // 5 seconds cooldown
    }
  };

  const value = {
    credits,
    loading: effectiveLoading,
    tier,
    pro_mode: proMode,
    error,
    creditResponse: creditResponse || null,
    subscriptionPlansResponse: subscriptionPlansResponse || null,
    refreshCredits,
    upgradeDetails,
    currentSubscriptionDetail,
    getUpgradeTierName
  };

  return <CreditsContext.Provider value={value}>{children}</CreditsContext.Provider>;
}

export const useCredits = () => {
  const context = useContext(CreditsContext);
  if (!context) {
    throw new Error("useCredits must be used within a CreditsProvider");
  }
  return context;
};