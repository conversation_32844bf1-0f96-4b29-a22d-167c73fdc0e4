import React, { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { Plus, X } from "lucide-react";
import { createPortal } from "react-dom";
import { useTabState } from "@/components/TabBar";
import { useToast } from "@/hooks/use-toast";
import {
  useLazyGetUserPromptsQuery,
  UserPrompt,
} from "@/store/api/promodeApiSlice";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import BotIcon from "@/assets/robots/research.svg";
import NormalRobot from "@/components/icons/Robots/NormalRobot";
import PlusIcon from "@/components/icons/PlusIcon";
import { cn } from "@/lib/utils";
import MoreIcon from "@/components/icons/Actions/MoreIcon";
import EditIcon from "@/components/icons/Actions/EditIcon";
import DeleteIcon from "@/components/icons/Actions/DeleteIcon";
import SettingsIcon from "@/components/icons/Actions/SettingsIcon";


interface ManageAgentsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

interface AgentCardProps {
  agent: UserPrompt;
  onEdit: (agent: UserPrompt) => void;
  onDelete: (agent: UserPrompt) => void;
}

interface NewAgentCardProps {
  onClick: () => void;
  activeTab: "main" | "sub";
}

// Agent Card Shimmer Component
const AgentCardSkeleton: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-[#1C1C1F] border border-[#FFFFFF0A] rounded-[16px] p-4 min-h-[200px] relative"
    >
      {/* Agent Icon skeleton */}
      <div className="w-12 h-12 rounded-full bg-[#FFFFFF0F] mb-4" />

      {/* Agent Info skeleton */}
      <div className="mb-6">
        <div className="bg-[#FFFFFF08] rounded h-5 mb-2 w-3/4" />
        <div className="bg-[#FFFFFF05] rounded h-4 mb-1 w-full" />
        <div className="bg-[#FFFFFF05] rounded h-4 w-2/3" />
      </div>

      {/* Stats skeleton */}
      <div className="flex items-center gap-4">
        <div className="bg-[#FFFFFF0A] rounded-full px-3 py-[6px] w-16 h-7" />
        <div className="bg-[#FFFFFF0A] rounded-full px-3 py-[6px] w-16 h-7" />
      </div>
    </motion.div>
  );
};

// New Agent Card Component
const NewAgentCard: React.FC<NewAgentCardProps> = ({ onClick, activeTab }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      onClick={onClick}
      className={cn(
        " border-[1px] border-dashed border-[#80FFF933] rounded-xl p-2  transition-colors cursor-pointer flex flex-col items-center justify-center min-h-[200px] group",
        activeTab !== "main" && "border-[#DD99FF33]"
      )}
    >
      <div
        className={cn(
          "bg-[#80FFF905] rounded-xl flex flex-col h-full w-full items-center justify-center",
          activeTab !== "main" && "bg-[#DD99FF05]"
        )}
      >
        <div className="w-12 h-12 rounded-full bg-[#80FFF905]  flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
          <PlusIcon
            size={24}
            color={activeTab === "main" ? "#80FFF9" : "#DD99FF"}
          />
        </div>
        <h3
          className={cn(
            "text-[#80FFF9] font-medium text-lg",
            activeTab !== "main" && "text-[#DD99FF]"
          )}
        >
          New {activeTab === "main" ? "Agent" : "Sub-agent"}
        </h3>
      </div>
    </motion.div>
  );
};

// Agent Card Component
const AgentCard: React.FC<AgentCardProps> = ({ agent, onEdit, onDelete }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  const dropdownRef = useRef<HTMLDivElement>(null);
  const moreButtonRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isDropdownOpen]);

  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    if (!isDropdownOpen && moreButtonRef.current) {
      const rect = moreButtonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 4,
        left: rect.right - 140 // Position dropdown to align right edge with button
      });
    }

    setIsDropdownOpen(!isDropdownOpen);
  };

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(agent);
    setIsDropdownOpen(false);
  };

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(agent);
    setIsDropdownOpen(false);
  };

  const handleCardClick = (e: React.MouseEvent) => {
    const target = e.target as HTMLElement;

    // Don't trigger edit if clicking on the more options area or dropdown
    if (
      (dropdownRef.current && dropdownRef.current.contains(target)) ||
      (moreButtonRef.current && moreButtonRef.current.contains(target))
    ) {
      return;
    }

    // Don't trigger edit if dropdown is open
    if (isDropdownOpen) {
      return;
    }

    onEdit(agent);
  };

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        onClick={handleCardClick}
        className={`bg-[#1C1C1F] border group-card overflow-clip border-[#FFFFFF0A] rounded-[16px] p-4 transition-colors relative group min-h-[200px] cursor-pointer ${
          !isDropdownOpen ? 'hover:bg-[#212124] hover:border-[#FFFFFF15]' : ''
        }`}
      >
        {/* Three dots menu */}
        <div className="absolute top-4 right-4">
          <div
            ref={moreButtonRef}
            onClick={handleMoreClick}
            className={`text-[#8A8F98] hover:text-[#E5EFFF] hover:bg-[#2D2D2F] rounded-[8px] bg-[#FFFFFF05] p-1 cursor-pointer transition-all duration-200 ${
              isDropdownOpen
                ? 'opacity-100'
                : 'opacity-0 group-hover:opacity-100'
            }`}
            title="Options"
          >
            <MoreIcon backgroundColor="none"/>
          </div>
        </div>

      {/* Agent Icon */}
      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-[#FFFFFF0F] mb-4">
        <img src={BotIcon} alt="Agent" className="w-6 h-6" />
      </div>

      {/* Agent Info */}
      <div className="mb-6 space-y-1">
        <h3 className="text-[#E6E6E6] font-medium capitalize">
          {agent.prompt_name.replaceAll("_", " ")}
        </h3>
        <p className="text-[#666666] text-[14px] font-['Inter'] line-clamp-2">
          {agent.skill_description || agent.description || "Custom Agent"}
        </p>
      </div>

        {/* Stats */}
        <div className={`flex items-center gap-4 text-[#8A8F98] text-sm transition-colors ${
          !isDropdownOpen ? 'group-hover:text-[#D9D9D9]' : ''
        }`}>
          <div className="flex items-center gap-1 bg-[#FFFFFF0A] rounded-full px-3 py-[6px]">
            <div className="relative">
              <NormalRobot size={16} primaryColor="#808080" className={!isDropdownOpen ? "group-hover:hidden" : ""} />
              <NormalRobot size={16} primaryColor="#D9D9D9" className={!isDropdownOpen ? "hidden group-hover:block" : "hidden"} />
            </div>
            <span>{agent.prompt_subagents?.length || 0}</span>
          </div>
          <div className="flex items-center gap-1 bg-[#FFFFFF0A] rounded-full px-3 py-[6px]">
            <div className="relative">
              <SettingsIcon size={16} color="#808080" className={!isDropdownOpen ? "group-hover:hidden" : ""} />
              <SettingsIcon size={16} color="#D9D9D9" className={!isDropdownOpen ? "hidden group-hover:block" : "hidden"} />
            </div>
            <span>{agent.prompt_tools?.length || 0}</span>
          </div>
        </div>
      </motion.div>

      {/* Dropdown Portal */}
      {isDropdownOpen && createPortal(
        <div
          ref={dropdownRef}
          className="fixed z-[9999] p-[6px] bg-[#18181A] backdrop-blur-sm rounded-[12px] border border-[#242424] shadow-lg min-w-[140px] pointer-events-auto"
          style={{
            top: dropdownPosition.top,
            left: dropdownPosition.left
          }}

        >
          <button
            type="button"
            onClick={handleEdit}
            className="flex items-center gap-2 rounded-[8px] w-full p-2 text-left text-[#808080] hover:text-white hover:bg-[#FFFFFF0A] transition-colors duration-150 group"
          >
            <div className="flex items-center justify-center w-5 h-5">
              <EditIcon width={20} height={20} fill="#808080" fillOpacity={0.6} className="group-hover:hidden" />
              <EditIcon width={20} height={20} fill="white" fillOpacity={1} className="hidden group-hover:block" />
            </div>
            <span className="font-medium text-[14px]">Edit</span>
          </button>
          {/* <button
            type="button"
            onClick={handleDelete}
            className="flex items-center gap-2 rounded-[8px] w-full p-2 text-left text-[#808080] hover:text-[#ED5B5B] hover:bg-[#ED5B5B0A] transition-colors duration-150 group"
          >
            <div className="flex items-center justify-center w-5 h-5">
              <DeleteIcon width={20} height={20} fill="#808080" className="group-hover:hidden" />
              <DeleteIcon width={20} height={20} fill="#ED5B5B" className="hidden group-hover:block" />
            </div>
            <span className="font-medium text-[14px]">Delete</span>
          </button> */}
        </div>,
        document.body
      )}
    </>
  );
};

export const ManageAgentsModal: React.FC<ManageAgentsModalProps> = ({
  isOpen,
  onOpenChange,
}) => {
  const { setTabs, setActiveTab: setActiveTabState } = useTabState();
  const { toast } = useToast();

  const [agents, setAgents] = useState<UserPrompt[]>([]);
  const [subAgents, setSubAgents] = useState<UserPrompt[]>([]);
  const [activeTab, setActiveTab] = useState<"main" | "sub">("main");

  // API hooks
  const [getUserPrompts, { isLoading, error }] = useLazyGetUserPromptsQuery();

  // Fetch user agents when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchAgents();
    }
  }, [isOpen]);

  const fetchAgents = async () => {
    try {
      const [mainAgentsResult, subAgentsResult] = await Promise.all([
        getUserPrompts({ is_main_agent: true }),
        getUserPrompts({ is_main_agent: false }),
      ]);

      if (mainAgentsResult.data) {
        setAgents(mainAgentsResult.data.user_prompts || []);
      }
      if (subAgentsResult.data) {
        setSubAgents(subAgentsResult.data.user_prompts || []);
      }
    } catch (error) {
      console.error("Failed to fetch agents:", error);
      toast({
        title: "Error",
        description: "Failed to load agents. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleCreateAgent = () => {
    if (activeTab === "main") {
      // Generate a unique tab ID for the create agent screen
      const newTabId = `create-agent-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Create new tab for create agent
      setTabs((prevTabs) => [
        ...prevTabs,
        {
          id: newTabId,
          title: "Create New Agent",
          path: "/create-agent",
          state: {
            tabId: newTabId,
            fromManageAgentsModal: true,
          },
        },
      ]);

      // Set the new tab as active
      setActiveTabState(newTabId);
    } else {
      // Generate a unique tab ID for the create subagent screen
      const newTabId = `create-subagent-${Date.now()}-${Math.random()
        .toString(36)
        .substring(2, 11)}`;

      // Create new tab for create subagent
      setTabs((prevTabs) => [
        ...prevTabs,
        {
          id: newTabId,
          title: "Create New Subagent",
          path: "/create-subagent",
          state: {
            tabId: newTabId,
            fromManageAgentsModal: true,
          },
        },
      ]);

      // Set the new tab as active
      setActiveTabState(newTabId);
    }

    // Close the modal
    onOpenChange(false);
  };

  const handleEditAgent = (agent: UserPrompt) => {
    // Generate a unique tab ID for the edit agent screen
    const newTabId = `edit-${
      agent.is_main_agent ? "agent" : "subagent"
    }-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    // Determine the correct path and title based on agent type
    const path = agent.is_main_agent ? "/create-agent" : "/create-subagent";
    const title = `Edit ${agent.prompt_name}`;

    // Create new tab for edit agent/subagent (reuse create screens with edit mode)
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title,
        path,
        state: {
          tabId: newTabId,
          editMode: true,
          agentData: agent,
          fromManageAgentsModal: true,
        },
      },
    ]);

    // Set the new tab as active
    setActiveTabState(newTabId);

    // Close the modal
    onOpenChange(false);
  };

  const handleDeleteAgent = (agent: UserPrompt) => {
    // For now, just show a toast - you can implement actual delete functionality later
    toast({
      title: "Delete Agent",
      description: `Delete functionality for "${agent.prompt_name}" will be implemented soon.`,
      variant: "default",
    });
  };

  if (error) {
    console.error("Error loading agents:", error);
  }

  const currentAgents = activeTab === "main" ? (agents || []) : (subAgents || []);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[700px] h-[700px] flex  w-full flex-col items-start max-h-[700px] bg-[#18181A] border border-[#2D2D2F] text-white overflow-hidden p-0">
        {/* Header */}
        <div className="w-full h-fit">
          <div className="flex items-center justify-between p-6">
            <div className="space-y-2">
              <h1 className="text-[18px] font-medium text-[#E8E8E6]">
                Manage Agents
              </h1>
              <p className="text-[#494A4D] font-['Inter'] text-[14px] font-medium">
                Create, edit and manage your custom agents
              </p>
            </div>
            <button
              type="button"
              title="Close"
              onClick={() => onOpenChange(false)}
              className="text-[#8A8F98] h-8 w-8 flex items-center justify-center absolute right-6 top-6 bg-[#FFFFFF0A] rounded-full hover:text-[#E5EFFF] hover:bg-[#FFFFFF10] backdrop-blur-lg"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-[#2D2D2F] px-6">
            <button
              type="button"
              onClick={() => setActiveTab("main")}
              className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === "main"
                  ? "border-[#E5EFFF] text-[#E5EFFF]"
                  : "border-transparent text-[#8A8F98] hover:text-[#E5EFFF]"
              }`}
            >
              Main agents
            </button>
            <button
              type="button"
              onClick={() => setActiveTab("sub")}
              className={`px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === "sub"
                  ? "border-[#E5EFFF] text-[#E5EFFF]"
                  : "border-transparent text-[#8A8F98] hover:text-[#E5EFFF]"
              }`}
            >
              Sub-agents
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 w-full h-full p-6 overflow-y-auto">
          {/* Loading State */}
          {isLoading && (
            <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* New Agent Card skeleton - only show for main agents */}
              {activeTab === "main" && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border-[1px] border-dashed border-[#80FFF933] rounded-xl p-2 min-h-[200px]"
                >
                  <div className="bg-[#80FFF905] rounded-xl flex flex-col h-full w-full items-center justify-center">
                    <div className="bg-[#80FFF910] rounded-full w-12 h-12 mb-4" />
                    <div className="bg-[#80FFF910] rounded w-20 h-5" />
                  </div>
                </motion.div>
              )}

              {/* Agent Card skeletons */}
              {Array.from({ length: 5 }).map((_, index) => (
                <AgentCardSkeleton key={index} />
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="py-12 text-center">
              <p className="mb-4 text-red-400">Failed to load agents</p>
              <Button
                onClick={fetchAgents}
                variant="outline"
                className="border-[#2D2D2F] text-[#8A8F98] hover:text-[#E5EFFF] hover:bg-[#2D2D2F]"
              >
                Try Again
              </Button>
            </div>
          )}

          {/* Agents Grid */}
          {!isLoading && !error && (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {/* New Agent Card - only show for main agents */}

              <NewAgentCard onClick={handleCreateAgent} activeTab={activeTab} />

              {/* Agent Cards */}
              {currentAgents.map((agent) => (
                <AgentCard
                  key={agent.id}
                  agent={agent}
                  onEdit={handleEditAgent}
                  onDelete={handleDeleteAgent}
                />
              ))}
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
