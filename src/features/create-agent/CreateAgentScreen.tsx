import React, { useState, useEffect, useCallback, useRef, useMemo } from "react";
import { motion } from "framer-motion";
import { RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useTabState } from "@/components/TabBar";
import { useToast } from "@/hooks/use-toast";
import { useConfig } from "@/hooks/useConfig";
import { useSelector, useDispatch } from "react-redux";
import DollarCyanSVG from "@/assets/fork/dollar_cyan.svg";
import ClockCyanSVG from "@/assets/fork/pace_cyan.svg";
import LikeCyanSVG from "@/assets/fork/like_cyan.svg";
import {
  selectCreateAgentFormData,
  setCreateAgentFormData,
  clearCreateAgentFormData,
  CreateAgentFormData,
} from "@/store/tabSlice";
import type { RootState } from "@/store";
import {
  useLazyGetBasePromptsQuery,
  useLazyGetAgentToolsQuery,
  useCreateUserPromptMutation,
  useUpdateUserPromptMutation,
  useLazyGetUserPromptsQuery,
} from "@/store/api/promodeApiSlice";
import {
  InstructionList,
  SystemConstraintSection,
  ToolItem,
  SubAgentItem,
} from "./components/SharedComponents";
import {
  SystemConstraintsSkeleton,
  ToolsListSkeleton,
  SubAgentsListSkeleton,
} from "./components/SkeletonComponents";

import WorkBenchIcon from "@/assets/pro/workbench.svg";
import { LearnMoreButton } from "@/components/ui/learn-more-button";
import InfoSquareIcon from "@/components/icons/InfoSquareIcon";
import ControlsIcon from "@/assets/controls.svg";


import NextArrow from "@/assets/pro/nextArrow.svg";
import BackGray from "@/assets/pro/back-gray.svg";
import PlusIcon from "@/components/icons/PlusIcon";

import Robot_1 from "@/assets/robots/robot_1.svg";
import Robot_2 from "@/assets/robots/robot_2.svg";
import Robot_3 from "@/assets/robots/robot_3.svg";
import Robot_4 from "@/assets/robots/robot_4.svg";
import Robot_5 from "@/assets/robots/robot_5.svg";
import Robot_6 from "@/assets/robots/robot_6.svg";
import Robot_7 from "@/assets/robots/robot_7.svg";
import Robot_8 from "@/assets/robots/robot_8.svg";
import Robot_9 from "@/assets/robots/robot_9.svg";
import Robot_10 from "@/assets/robots/robot_10.svg";
import ResearchSVG from "@/assets/robots/research.svg";
import FourDashIcon from "@/components/icons/FourDash";
import TextEditIcon from "@/components/icons/TextEdit";
import SettingIcon from "@/components/icons/SettingIcon";
import CircleDashedIcon from "@/components/icons/CircleDashedIcon";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { X } from "lucide-react";


import BackgroundSVG from "@/assets/pro/bg.svg";
import { DetailsModal as DetailsModalComponent } from "./components/DetailsModal";
import { LearnMoreModal } from "./components/LearnMoreModal";
import { cn } from "@/lib/utils";
import useScreenSize from "@/hooks/useScreenSize";
import EditIcon from "@/components/icons/Actions/EditIcon";




// Constants
const STEP_CONFIG = {
  1: {
    title: "Define your Agent",
    description:
      "Your agent is only as good as the instructions you give it: Think of this as writing a job description for your ideal AI teammate—be specific about who they are, what they do, and how they work.",
    icon: WorkBenchIcon,
    iconAlt: "Workbench",
    welcomeTitle: "Creating an Agent on Emergent",
    learnMoreText: "Learn about prompt design",
    tips:"The clearer you are, the better your agent performs."
  },
  2: {
    title: "Select Tools",
    description:
      "Equip your agent with the right capabilities: Tools are essential for your Agent to operate and work. Please choose the specialized tools required by your Agent as per your needs and goals.",
    icon: ControlsIcon,
    iconAlt: "Tools",
    welcomeTitle: "What exactly are tools?",
    learnMoreText: "Learn about tools",
    tips:"The tools selected by default are mandatorily required by the agents to function."
  },
  3: {
    title: "Configure Sub-agents",
    description: "Deploy specialized experts for complex tasks: Sub-agents are your agent's specialist consultants—highly focused AI helpers that excel in specific domains.",
    icon: ControlsIcon,
    iconAlt: "Sub-agents",
    welcomeTitle: "What are Sub-Agents ?",
    learnMoreText: "Learn about Sub-agents",
    tips:"Think of sub-agents as your agent's expert advisory team."
  },
  4: {
    title: "Review Agent Info",
    description: "Final check before launch: Please review your agent configuration before saving.",
    icon: WorkBenchIcon,
    iconAlt: "Review",
    welcomeTitle: "To Review : ",
    learnMoreText: "Learn about Agents",
    tips:"You can edit Everything after creation as well."
  },
};

const INSTRUCTION_ITEMS = [
  {
    boldText:"Name your agent",
    text: " based on its specialty and role",
  },
  {
    boldText: "Describe their role",
    text: " - who they are and what they're experts at",
  },
  {
    boldText: "Outline their workflow",
    text: " - step-by-step approach to solving problems",
    // highlight: "{{VARIABLE_NAME}}",
  },
  {
    boldText: "Set guidelines ",
    text: " - best practices, constraints, and things to avoid",
  },
  {
    text: "Always include subagents, and specify when exactly they need to be called.",
  },
];

const REVIEW_INSTRUCTION_ITEMS = [
  {
    boldText:"Agent Definition",
    text: " – Identity, workflow, and guidelines are set",
  },
  {
    boldText: "Tools Selected",
    text: " – Required tools are configured",
  },
  {
    boldText: "Sub-agents Select",
    text: " – Expert Subagent assigned for specific tasks",
    // highlight: "{{VARIABLE_NAME}}",
  },
  {
    boldText: "Set guidelines ",
    text: " - best practices, constraints, and things to avoid",
  },
];


const TOOLS_INSTRUCTION_ITEMS = [
  {
    text: "Each tool serves a specific function in your agent's workflow, choose them wisely.",
  },
  {
    text: "Include only essential tools to keep your agent focused and contextually aware, do not overload with too many tools",
  },
  {
    text: "Choose tools that directly support your agent's core objectives",
  },
];

const SUBAGENT_INSTRUCTION_ITEMS = [
  {
    text: "Each sub-agent masters one specific task, and they work as an extension of your main agent's capabilities",
  },
  {
    text: "In the sub-agent system prompt define the agent and what they're experts at, also map their workflow",
    // highlight: "{{VARIABLE_NAME}}",
  },
  {
    text: "Include guidelines, best practices, constraints, and things to avoid",
  },
   {
    text: "Be mindful, Sub-Agents cannot call other sub-agents, they can only interact with the main agent",
  },
];

const DEFAULT_SUBAGENTS = [
  {
    id: "fullstack-tester",
    name: "Fullstack tester",
    description: "Reviews code for security vulnerabilities",
    icon: Robot_1,
    defaultChecked: true,
  },
  {
    id: "backend-tester",
    name: "Backend tester",
    description: "Reviews code for security vulnerabilities",
    icon: Robot_2,
    defaultChecked: true,
  },
  {
    id: "frontend-tester",
    name: "Frontend tester",
    description: "Reviews code for security vulnerabilities",
    icon: Robot_3,
    defaultChecked: true,
  },
  {
    id: "integration-expert",
    name: "Integration expert",
    description: "Reviews code for security vulnerabilities",
    icon: Robot_4,
    defaultChecked: true,
  },
];

const SYSTEM_CONSTRAINT_SECTIONS = [
  {
    id: "system-constraints-1",
    title: "System Constraints",
    helpText: "What are system constraints",
    readOnly: true,
    showLock: true,
  },
  {
    id: "system-constraints-2",
    title: "System Constraints 2",
    helpText: "What are system constraints",
    readOnly: true,
    showLock: true,
  },
  {
    id: "system-prompt",
    title: "System Prompt",
    helpText: "Learn about system prompts",
    readOnly: true,
    showLock: false,
  },
];

const BUTTON_TEXTS = {
  1: "Continue to Add Tools",
  2: "Continue to Add Sub-Agents",
  3: "Review Agent",
  4: "Save Your Agent",
};

interface InfoInterface {
  title: string;
  description: string;
  icon: string;
}

export const INFO: InfoInterface[] = [
  {
    title: "Preserved session context",
    description: "All important context preserved in summary",
    icon: LikeCyanSVG,
  },

  {
    title: "Agent memory refreshed",
    description: "Fresh context window with clean slate",
    icon: ClockCyanSVG,
  },
  {
    title: "Less Spenditure",
    description: "Refreshed memory means lower token costs",
    icon: DollarCyanSVG,
  },
];

interface CreateAgentScreenProps {
  tabId: string;
}

// Reusable Edit Button Component
const EditButton: React.FC<{
  onClick: () => void;
  children: React.ReactNode;
}> = ({ onClick, children }) => (
  <button
    type="button"
    onClick={onClick}
    className="flex items-center gap-2 px-4 py-[2px] font-semibold text-[#18181A] transition-colors bg-white rounded-full hover:bg-opacity-90"
  >
    <EditIcon fill="#000000" fillOpacity={1} />
    {children}
  </button>
);

export const CreateAgentScreen: React.FC<CreateAgentScreenProps> = ({
  tabId,
}) => {
  // Robot icons array for selection - memoized to prevent re-creation on every render
  const ROBOT_ICONS = useMemo(() => [
    { id: "research", icon: ResearchSVG, name: "Research" },
    { id: "robot_1", icon: Robot_1, name: "Robot 1" },
    { id: "robot_2", icon: Robot_2, name: "Robot 2" },
    { id: "robot_3", icon: Robot_3, name: "Robot 3" },
    { id: "robot_4", icon: Robot_4, name: "Robot 4" },
    { id: "robot_5", icon: Robot_5, name: "Robot 5" },
    { id: "robot_6", icon: Robot_6, name: "Robot 6" },
    { id: "robot_7", icon: Robot_7, name: "Robot 7" },
    { id: "robot_8", icon: Robot_8, name: "Robot 8" },
    { id: "robot_9", icon: Robot_9, name: "Robot 9" },
    { id: "robot_10", icon: Robot_10, name: "Robot 10" },
  ], []);

  // Redux hooks for form persistence
  const dispatch = useDispatch();
  const persistedFormData = useSelector((state: RootState) =>
    selectCreateAgentFormData(state, tabId)
  );

  const [currentStep, setCurrentStep] = useState(1); // Temporarily set to 4 to show review
  const [agentName, setAgentName] = useState("");
  const [systemConstraints, setSystemConstraints] = useState(
    "You are a helpful AI assistant. Always follow safety guidelines You are a"
  );

  // Dynamic variables state - stores values for each dynamic variable
  const [dynamicVariableValues, setDynamicVariableValues] = useState<
    Record<string, string>
  >({});

  // Selected tools state
  const [selectedTools, setSelectedTools] = useState<string[]>([]);

  // Selected sub-agents state
  const [selectedSubAgents, setSelectedSubAgents] = useState<string[]>([]);

  // Icon selection state
  const [selectedIcon, setSelectedIcon] = useState(ROBOT_ICONS[0]); // Default to research icon
  const [isIconModalOpen, setIsIconModalOpen] = useState(false);

  // Details modal state
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [detailsModalData, setDetailsModalData] = useState<{
    type: "tool" | "subagent";
    data: any;
  } | null>(null);

  // Learn More modal state
  const [isLearnMoreModalOpen, setIsLearnMoreModalOpen] = useState(false);



  // Ref for scrollable content area
  const scrollableAreaRef = useRef<HTMLDivElement>(null);

  // Tab management and toast hooks
  const { removeTab, setActiveTab, setTabs, getActiveTab } = useTabState();
  const { toast } = useToast();

  // Get current tab data to check for edit mode
  const currentTab = getActiveTab();
  const isEditMode = currentTab?.state?.editMode || false;
  const agentData = currentTab?.state?.agentData;

  // Helper function to prettify name by replacing underscores with spaces and capitalizing
  const prettifyName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Config hook for agent configurations
  const { config, loading: configLoading } = useConfig();

  // Lazy fetch base prompts from API - only when needed
  const [
    getBasePrompts,
    { data: basePromptsData, isLoading: isLoadingPrompts, error: promptsError },
  ] = useLazyGetBasePromptsQuery();

  // Lazy fetch agent tools - only when needed
  const [
    getAgentTools,
    { data: agentToolsData, isLoading: isLoadingTools, error: toolsError },
  ] = useLazyGetAgentToolsQuery();

  // Lazy fetch user subagents - only when needed
  const [
    getUserSubagents,
    {
      data: userSubagentsData,
      isLoading: isLoadingSubagents,
      error: subagentsError,
    },
  ] = useLazyGetUserPromptsQuery();

  // Create and update user prompt mutations
  const [createUserPrompt, { isLoading: isCreatingPrompt }] =
    useCreateUserPromptMutation();
  const [updateUserPrompt, { isLoading: isUpdatingPrompt }] =
    useUpdateUserPromptMutation();

  // Helper function to find the main agent prompt
  const getMainPrompt = () => {
    if (!basePromptsData?.prompts || basePromptsData.prompts.length === 0) {
      return null;
    }

    // Find prompt whose prompt_name includes "main"
    const mainPrompt = basePromptsData.prompts.find((prompt) =>
      prompt.prompt_name.toLowerCase().includes("main")
    );

    // Fallback to first prompt if no main prompt found
    return mainPrompt || basePromptsData.prompts[0];
  };

  // Fetch base prompts when component mounts
  useEffect(() => {
    getBasePrompts();
  }, [getBasePrompts]);

  // Initialize dynamic variable values with default values when base prompts data is loaded
  useEffect(() => {
    if (basePromptsData?.prompts && basePromptsData.prompts.length > 0) {
      // Find the main agent prompt (contains "main" in prompt_name)
      const mainPrompt = basePromptsData.prompts.find((prompt) =>
        prompt.prompt_name.toLowerCase().includes("main")
      );
      const selectedPrompt = mainPrompt || basePromptsData.prompts[0];
      const defaultValues = selectedPrompt.default_variable_values || {};

      // Only set default values if dynamicVariableValues is empty (first load)
      if (Object.keys(dynamicVariableValues).length === 0) {
        setDynamicVariableValues(defaultValues);
      }
    }
  }, [basePromptsData, dynamicVariableValues]);

  // Initialize form with persisted data or existing data in edit mode
  useEffect(() => {
    if (isEditMode && agentData) {
      // In edit mode, use agent data
      setAgentName(agentData.prompt_name || "");
      setSelectedTools(agentData.prompt_tools || []);
      setSelectedSubAgents(agentData.prompt_subagents || []);
      setDynamicVariableValues(agentData.prompt_variables || {});

      // Initialize icon if available, otherwise keep default
      if (agentData.icon_id) {
        const foundIcon = ROBOT_ICONS.find(
          (icon) => icon.id === agentData.icon_id
        );
        if (foundIcon) {
          setSelectedIcon(foundIcon);
        }
      }

      // Start at step 4 (review) in edit mode
      setCurrentStep(4);
    }
  }, [isEditMode, agentData]);

  // Fetch tools data when in edit mode and we have base prompts data but no tools data
  useEffect(() => {
    if (
      isEditMode &&
      basePromptsData?.prompts &&
      basePromptsData.prompts.length > 0 &&
      !agentToolsData?.data &&
      (agentData?.prompt_tools?.length > 0) // Only fetch if there are tools to display
    ) {
      // Find the main agent prompt (contains "main" in prompt_name)
      const mainPrompt = basePromptsData.prompts.find((prompt) =>
        prompt.prompt_name.toLowerCase().includes("main")
      );
      const selectedPrompt = mainPrompt || basePromptsData.prompts[0];
      const parentPrompt = selectedPrompt.prompt_name;
      getAgentTools(parentPrompt);
    }
  }, [isEditMode, basePromptsData, agentToolsData, agentData, getAgentTools]);

  // Separate effect for restoring persisted data - runs when component mounts or when switching back from edit mode
  useEffect(() => {

    if (persistedFormData && !isEditMode) {
      // Restore persisted form data when returning from subagent editing
      setAgentName(persistedFormData.agentName);
      setSystemConstraints(persistedFormData.systemConstraints);
      setDynamicVariableValues(persistedFormData.dynamicVariableValues);
      setSelectedTools(persistedFormData.selectedTools);
      setSelectedSubAgents(persistedFormData.selectedSubAgents);
      setSelectedIcon(persistedFormData.selectedIcon);
      setCurrentStep(persistedFormData.currentStep);
    }
  }, [persistedFormData, isEditMode]);
  // Memoized function to save form data to Redux
  const saveFormData = useCallback(() => {
    if (!isEditMode) {
      const formData: CreateAgentFormData = {
        agentName,
        systemConstraints,
        dynamicVariableValues,
        selectedTools,
        selectedSubAgents,
        selectedIcon,
        currentStep,
      };
      console.log('CreateAgentScreen - Saving form data:', { tabId, formData });
      dispatch(setCreateAgentFormData({ tabId, formData }));
    }
  }, [
    agentName,
    systemConstraints,
    dynamicVariableValues,
    selectedTools,
    selectedSubAgents,
    selectedIcon,
    currentStep,
    isEditMode,
    dispatch,
    tabId,
  ]);

  const {isMobile} = useScreenSize();

  // Save form data to localStorage with debouncing (only in create mode)
  useEffect(() => {
    const timeoutId = setTimeout(saveFormData, 300); // 300ms debounce
    return () => clearTimeout(timeoutId);
  }, [saveFormData]);

  // Auto-resize textarea when content changes
  useEffect(() => {
    const textarea = document.getElementById(
      "system-prompts-readonly"
    ) as HTMLTextAreaElement;
    if (textarea && getMainPrompt()?.prompt) {
      textarea.style.height = "auto";
      textarea.style.height = Math.min(textarea.scrollHeight, 500) + "px";
    }
  }, [basePromptsData]);

  // Reset scroll position when step changes
  useEffect(() => {
    if (scrollableAreaRef.current) {
      scrollableAreaRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  // Fetch agent tools when we have base prompts data and user is on step 2 or step 4 (review)
  useEffect(() => {
    if (
      (currentStep === 2 || currentStep === 4) &&
      basePromptsData?.prompts &&
      basePromptsData.prompts.length > 0 &&
      !agentToolsData?.data // Only fetch if we don't already have the data
    ) {
      // Find the main agent prompt (contains "main" in prompt_name)
      const mainPrompt = basePromptsData.prompts.find((prompt) =>
        prompt.prompt_name.toLowerCase().includes("main")
      );
      const selectedPrompt = mainPrompt || basePromptsData.prompts[0];
      const parentPrompt = selectedPrompt.prompt_name; // This is the agent_name for the tools API
      getAgentTools(parentPrompt);
    }
  }, [currentStep, basePromptsData, getAgentTools, agentToolsData]);

  // Fetch user subagents when user is on step 3
  useEffect(() => {
    if (currentStep === 3) {
      getUserSubagents({ is_main_agent: false });
    }
  }, [currentStep, getUserSubagents]);

  // Initialize default sub-agents when we have config data and base prompts data
  useEffect(() => {
    if (
      config?.agent_names &&
      basePromptsData?.prompts &&
      basePromptsData.prompts.length > 0
    ) {
      // Find the main agent prompt (contains "main" in prompt_name)
      const mainPrompt = basePromptsData.prompts.find((prompt) =>
        prompt.prompt_name.toLowerCase().includes("main")
      );
      const selectedPrompt = mainPrompt || basePromptsData.prompts[0];
      const agentName = selectedPrompt.prompt_name;

      // Find the agent configuration by name
      const agentConfig = config.agent_names.find(
        (agent: any) => agent.name === agentName
      );

      if (agentConfig?.sub_agents && selectedSubAgents.length === 0) {
        // Set default sub-agents only if none are selected yet
        setSelectedSubAgents(agentConfig.sub_agents);
      }
    }
  }, [config, basePromptsData, selectedSubAgents]);

  // Automatically select all tools when agent tools data is loaded
  useEffect(() => {
    if (agentToolsData?.data && Array.isArray(agentToolsData.data) && agentToolsData.data.length > 0) {
      // Select all tools (both necessary and non-necessary) by default
      const allTools = agentToolsData.data.map((tool) => tool.name);

      if (allTools.length > 0) {
        setSelectedTools((prev) => {
          // Add all tools if they're not already selected
          const newTools = [...prev];
          allTools.forEach((toolName) => {
            if (!newTools.includes(toolName)) {
              newTools.push(toolName);
            }
          });
          return newTools;
        });
      }
    }
  }, [agentToolsData]);

  // Generate dynamic system constraint sections based on API response
  const generateDynamicSections = () => {
    if (!basePromptsData?.prompts || basePromptsData.prompts.length === 0) {
      return SYSTEM_CONSTRAINT_SECTIONS; // Fallback to static sections
    }

    // Find the main agent prompt and get its dynamic variables
    const mainPrompt = basePromptsData.prompts.find((prompt) =>
      prompt.prompt_name.toLowerCase().includes("main")
    );
    const selectedPrompt = mainPrompt || basePromptsData.prompts[0];
    const dynamicVariables = selectedPrompt.dynamic_variables || [];

    // Generate sections for each dynamic variable
    const dynamicSections = dynamicVariables.map(
      (variable: string, index: number) => ({
        id: `dynamic-variable-${index}`,
        title: variable
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l: string) => l.toUpperCase()), // Convert snake_case to Title Case
        helpText: `Learn about ${variable.toLowerCase()}`,
        readOnly: false,
        showLock: false,
        isDynamic: true,
        variableName: variable,
      })
    );

    return dynamicSections;
  };

  const systemConstraintSections = generateDynamicSections();

  // Handler for dynamic variable changes
  const handleDynamicVariableChange = (variableName: string, value: string) => {
    setDynamicVariableValues((prev) => ({
      ...prev,
      [variableName]: value,
    }));
  };

  // Handler for tool selection with checked state
  const handleToolCheckedChange = (toolName: string, checked: boolean) => {
    // Check if this is a necessary tool - if so, don't allow toggling
    const tool = agentToolsData?.data?.find((t) => t.name === toolName);
    if (tool?.necessary_tool) {
      return; // Don't allow toggling necessary tools
    }

    setSelectedTools((prev) => {
      if (checked) {
        return prev.includes(toolName) ? prev : [...prev, toolName];
      } else {
        return prev.filter((name) => name !== toolName);
      }
    });
  };

  // Handler for sub-agent selection with checked state
  const handleSubAgentCheckedChange = (
    subAgentName: string,
    checked: boolean
  ) => {
    setSelectedSubAgents((prev) => {
      if (checked) {
        return prev.includes(subAgentName) ? prev : [...prev, subAgentName];
      } else {
        return prev.filter((name) => name !== subAgentName);
      }
    });
  };

  // Handler for creating a new subagent
  const handleCreateSubagent = () => {
    // Generate a unique tab ID for the create subagent screen
    const newTabId = `create-subagent-${Date.now()}-${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    // Create new tab for create subagent
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: "Create New Subagent",
        path: "/create-subagent",
        state: {
          tabId: newTabId,
          fromCreateAgent: true,
        },
      },
    ]);

    // Set the new tab as active
    setActiveTab(newTabId);
  };

  // Handler for refreshing subagents
  const handleRefreshSubagents = () => {
    getUserSubagents({ is_main_agent: false });
  };

  // Handler for editing a subagent
  const handleEditSubagent = (subagent: any) => {
    // Find the full subagent data from the API response
    const fullSubagentData = userSubagentsData?.user_prompts?.find(
      (s) => s.prompt_name === subagent.name
    );

    if (!fullSubagentData) {
      toast({
        title: "Error",
        description: "Subagent data not found. Please try refreshing.",
        variant: "destructive",
        duration: 5000,
      });
      return;
    }

    // Generate a unique tab ID for the edit subagent screen
    const newTabId = `edit-subagent-${Date.now()}-${Math.random()
      .toString(36)
      .substring(2, 11)}`;

    // Create new tab for edit subagent (reuse create screen with edit mode)
    setTabs((prevTabs) => [
      ...prevTabs,
      {
        id: newTabId,
        title: `Edit ${fullSubagentData.prompt_name}`,
        path: "/create-subagent",
        state: {
          tabId: newTabId,
          editMode: true,
          agentData: fullSubagentData,
          fromCreateAgent: true,
        },
      },
    ]);

    // Set the new tab as active
    setActiveTab(newTabId);
  };

  // Check if we can proceed to the next step or save
  const canProceed = () => {
    if (currentStep === 1) {
      // Step 1: Agent name is required + all dynamic variables must be filled
      if (agentName.trim().length === 0) {
        return false;
      }

      // Check if all dynamic variables are filled
      const mainPrompt = getMainPrompt();
      if (mainPrompt?.dynamic_variables) {
        for (const variable of mainPrompt.dynamic_variables) {
          const value = dynamicVariableValues[variable];
          if (!value || value.trim().length === 0) {
            return false;
          }
        }
      }

      return true;
    }
    if (currentStep === 4) {
      return (
        agentName.trim().length > 0 &&
        basePromptsData?.prompts &&
        basePromptsData.prompts.length > 0
      ); // Need agent name and base prompts
    }
    return true; // Other steps don't have validation for now
  };

  const handleContinue = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
      // Reset scroll position when moving to next step
      if (scrollableAreaRef.current) {
        scrollableAreaRef.current.scrollTop = 0;
      }
    } else {
      // Complete agent creation - close tab, activate home, show toast
      handleSaveAgent();
    }
  };

  const handleSaveAgent = async () => {
    try {
      // Find the main agent prompt (contains "main" in prompt_name)
      const mainPrompt = basePromptsData?.prompts?.find((prompt) =>
        prompt.prompt_name.toLowerCase().includes("main")
      );

      // Fallback to first prompt if no main prompt found
      const selectedPrompt = mainPrompt || basePromptsData?.prompts?.[0];
      const basePromptId = selectedPrompt?.id;

      if (
        !basePromptId ||
        !basePromptsData?.prompts ||
        basePromptsData.prompts.length === 0 ||
        !selectedPrompt
      ) {
        toast({
          title: "Error",
          description: "No main agent base prompt available. Please try again.",
          variant: "destructive",
          duration: 5000,
        });
        return;
      }

      // Prepare the request data
      const requestData = {
        prompt_name: agentName || "My Agent",
        parent_prompt: selectedPrompt.prompt_name, // Dynamic parent prompt from selected main prompt
        base_prompt_id: basePromptId,
        prompt_subagents: selectedSubAgents, // Use selected sub-agents
        prompt_tools: selectedTools.length > 0 ? selectedTools : null, // Use selected tools or null if none selected
        prompt_variables: dynamicVariableValues,
        system_prompt_injection_templates: [
          "integration_playbook_expert_prompt",
        ], // You can make this dynamic
        is_main_agent: "true",
        icon_id: selectedIcon.id, // Include selected icon
      };

      // Make the API call - create or update based on mode
      if (isEditMode && agentData?.id) {
        await updateUserPrompt({
          id: agentData.id,
          data: requestData,
        }).unwrap();
      } else {
        await createUserPrompt(requestData).unwrap();
      }

      // Close the current tab
      removeTab(tabId);

      // Activate the home tab - modal will be reopened automatically if needed
      // Note: We no longer need to navigate back to manage agents tab since it's now a modal
      if (
        currentTab?.state?.fromManageAgents ||
        currentTab?.state?.fromManageAgentsModal
      ) {
        // Just go to home - the modal can be reopened by the user if needed
        setActiveTab("home");
      } else {
        setActiveTab("home");
      }

      // Clear persisted form data on successful creation/update
      if (!isEditMode) {
        dispatch(clearCreateAgentFormData({ tabId }));
      }

      // Show success toast
      toast({
        title: isEditMode ? "Agent Updated" : "Agent Created",
        description: isEditMode
          ? "Your agent has been successfully updated."
          : "Your agent has been successfully created and saved.",
        duration: 5000,
      });
    } catch (error) {
      console.error("Failed to create agent:", error);
      toast({
        title: "Error",
        description: "Failed to create agent. Please try again.",
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  // Handler functions for details modal
  const handleToolViewDetails = (tool: any) => {
    setDetailsModalData({
      type: "tool",
      data: tool,
    });
    setIsDetailsModalOpen(true);
  };

  const handleSubagentViewDetails = (subagent: any) => {
    setDetailsModalData({
      type: "subagent",
      data: subagent,
    });
    setIsDetailsModalOpen(true);
  };

  const cleanInput = (value) => {
    return value
      .replace(/[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000]/g, ' ') // Replace various Unicode spaces with regular space
      .replace(/[\u2010-\u2015]/g, '-') // Replace various Unicode dashes with regular hyphen
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
  };

  // Icon Selection Modal Component
  const IconSelectionModal = () => (
    <Dialog open={isIconModalOpen} onOpenChange={setIsIconModalOpen}>
      <DialogContent className="max-w-md p-0 bg-[#18181A] border-[#242424]">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Choose Icon</h3>
            <DialogClose asChild>
              <button
                type="button"
                aria-label="Close icon selection modal"
                className="text-[#737780] hover:text-white transition-colors"
              >
                <X size={20} />
              </button>
            </DialogClose>
          </div>

          <div className="grid grid-cols-3 gap-4">
            {ROBOT_ICONS.map((robotIcon) => (
              <button
                type="button"
                key={robotIcon.id}
                onClick={() => {
                  setSelectedIcon(robotIcon);
                  setIsIconModalOpen(false);
                }}
                className={`
                  p-4 rounded-lg border-2 transition-all duration-200 hover:scale-105
                  ${
                    selectedIcon.id === robotIcon.id
                      ? "border-white bg-[#FFFFFF10]"
                      : "border-[#262629] bg-[#FFFFFF05] hover:border-[#404040]"
                  }
                `}
              >
                <div className="flex flex-col items-center gap-2">
                  <img
                    src={robotIcon.icon}
                    alt={robotIcon.name}
                    className="w-8 h-8"
                  />
                  <span className="text-xs text-[#B7BFCC] font-medium">
                    {robotIcon.name}
                  </span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  // Import the shared details modal
  const DetailsModal = () => {
    if (!detailsModalData) return null;

    return (
      <DetailsModalComponent
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        type={detailsModalData.type}
        data={detailsModalData.data}
      />
    );
  };

  if(isMobile){
    return <section className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="max-w-md mx-auto">
        <h2 className="mb-4 text-2xl font-semibold">Desktop Required</h2>
        <p className="mb-6 text-gray-600">
          Creating agents requires a desktop experience for the best workflow.
          Please open this page on a desktop or laptop computer.
        </p>
        <div className="text-sm text-gray-500">
           Switch to desktop to continue
        </div>
      </div>
    </section>;
  }

  return (
    <section
      style={{
        height: "calc(100vh - 56px)",
      }}
      className="flex w-full h-full bg-[#0A0A0B] text-white"
    >
      <img
        src={BackgroundSVG}
        alt="Background"
        className="absolute top-0 left-0 object-cover w-full h-full"
      />
      {/* First Section - Header with Step Indicator */}
      <div className="px-[120px] h-full max-w-[45%] z-[10]  flex items-center justify-center py-8">
        <div className="m-auto space-y-[32px] ">
          {/* Step Indicator */}
          <div className="space-y-4">
            {currentStep !== 4 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className=""
              >
                <div className="text-[#F8FF99] flex gap-2 font-medium font-nothing">
                  Step <span className="font-nothing">{currentStep} / 3</span>
                </div>
              </motion.div>
            )}

            {/* Main Title and Description */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className=""
            >
              <h1 className="mb-4 text-4xl font-semibold leading-tight text-white">
                {isEditMode && currentStep === 4
                  ? `Edit ${prettifyName(agentName || "Agent")}`
                  : STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.title}
              </h1>
              <span className='text-[#737780] text-[14px] font-medium font-["Inter"]'>
                {
                  STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                    ?.description
                }
              </span>
            </motion.div>
          </div>

          <div className="h-[1px] bg-[#1F1F21] w-full"></div>

          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="rounded-xl"
          >
            <div className="flex items-start gap-4">
              <div className="flex-1">
                <h2 className="flex items-center gap-2 mb-3 text-xl font-semibold text-[#B7BECC]">
                  <img
                    src={
                      STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.icon
                    }
                    alt={
                      STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                        ?.iconAlt
                    }
                    className="inline-block w-6 h-6 mb-1"
                  />
                  {
                    STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                      ?.welcomeTitle
                  }
                </h2>

                {currentStep === 1 && (
                  <InstructionList items={INSTRUCTION_ITEMS} />
                )}
                {currentStep === 2 && (
                  <InstructionList items={TOOLS_INSTRUCTION_ITEMS} />
                )}
                {currentStep === 3 && (
                  <InstructionList items={SUBAGENT_INSTRUCTION_ITEMS} />
                )}
                {currentStep === 4 && (
                  <InstructionList items={REVIEW_INSTRUCTION_ITEMS} />
                )}

                <span className="text-[#ffffff]/80 italic text-[16px] font-medium font-['Inter']">
                  {STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.tips}
                </span>

                {/* Learn More Button */}
                {currentStep != 4 && (
                  <LearnMoreButton
                    text={
                      STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                        ?.learnMoreText || "Learn More"
                    }
                    className="mt-6"
                    onClick={() => setIsLearnMoreModalOpen(true)}
                  />
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Second Section - Form Fields */}
      <div className="w-[55%] p-6 h-full overflow-hidden relative">
        <div className="border w-full h-full bg-[#18181A] rounded-[8px] border-[#222224] relative flex flex-col">
          {/* Scrollable Content Area */}
          <div
            ref={scrollableAreaRef}
            className="scrollable-content-area flex-1 overflow-y-auto pt-[56px] pb-[105px]"
          >
            {/* Dynamic Content Based on Step */}
            {currentStep === 1 ? (
              // Step 1: Basic Info and Agent Prompt
              <div className="space-y-[40px] pb-[40px]">
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center gap-2">
                    <InfoSquareIcon size={24} color="#FFFFFF" />
                    <h2 className="text-[16px] font-medium text-[#FFFFFF4D]">
                      Basic Info
                    </h2>
                  </div>

                  <div className="flex rounded-[16px] w-full gap-6 p-8 border border-[#222224] ">
                    <div className="flex flex-col w-full gap-3 ">
                      <div className="flex items-center justify-between">
                        <Label
                          htmlFor="agent-name"
                          className="font-['Inter'] font-medium text-[#e4eeff]"
                        >
                          Agent Name
                        </Label>
                      </div>
                      <Input
                        id="agent-name"
                        value={isEditMode ? prettifyName(agentName) : agentName}
                        onChange={(e) => setAgentName(cleanInput(e.target.value))}
                        placeholder="For eg : Research Agent, Automation Engineer, ..."
                        disabled={isEditMode}
                        className="!bg-[#FFFFFF05] font-['Inter'] h-[56px] max-h-[56px] !text-[16px] border-[#262629] text-white placeholder:text-white/20 focus:border-white/30 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 [&:-webkit-autofill]:!bg-[#FFFFFF05] [&:-webkit-autofill]:text-white [&:-webkit-autofill]:shadow-[inset_0_0_0px_1000px_rgba(255,255,255,0.02)] [&:-webkit-autofill:hover]:!bg-[#FFFFFF05] [&:-webkit-autofill:focus]:!bg-[#FFFFFF05] [&:-webkit-autofill:active]:!bg-[#FFFFFF05] disabled:opacity-60 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="flex flex-col gap-3">
                      <Label
                        htmlFor="agent-icon"
                        className="font-['Inter'] font-medium text-[#e4eeff]"
                      >
                        Icon
                      </Label>
                      <button
                        type="button"
                        onClick={() => setIsIconModalOpen(true)}
                        className="max-h-[56px] h-[56px] min-w-[56px] flex items-center justify-center rounded-[8px] border-[#262629] bg-[#FFFFFF05] border hover:border-[#404040] hover:bg-[#FFFFFF10] transition-all duration-200"
                      >
                        <img
                          src={selectedIcon.icon}
                          alt={selectedIcon.name}
                          className="w-7 h-7"
                        />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Agent Prompt Section */}
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center gap-2">
                    <TextEditIcon size={24} color="#FFFFFF" />
                    <h2 className="text-[16px] font-medium text-[#FFFFFF4D]">
                      Agent Prompt
                    </h2>
                  </div>

                  {/* System Constraints */}
                  <div className="flex flex-col gap-12 border border-[#222224] p-8  rounded-[16px]">
                    {isLoadingPrompts ? (
                      <SystemConstraintsSkeleton count={3} />
                    ) : promptsError ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-red-400">
                          Error loading prompts. Using default sections.
                        </div>
                      </div>
                    ) : (
                      <>
                        {systemConstraintSections.map((section) => (
                          <SystemConstraintSection
                            key={section.id}
                            section={section}
                            value={systemConstraints}
                            onChange={setSystemConstraints}
                            dynamicVariableValues={dynamicVariableValues}
                            onDynamicVariableChange={
                              handleDynamicVariableChange
                            }
                          />
                        ))}

                        <SystemConstraintSection
                          key="system-prompts"
                          section={{
                            id: "system-prompts-readonly",
                            title: "System Constraints",
                            helpText: "Learn about system prompts",
                            readOnly: true,
                            showLock: true,
                            isDynamic: false,
                          }}
                          value={getMainPrompt()?.prompt || ""}
                          onChange={() => {}} // No-op since it's read-only
                        />
                      </>
                    )}
                  </div>
                </div>
              </div>
            ) : currentStep === 2 ? (
              // Step 2: Tools Selection
              <div className="space-y-[40px] pb-[40px]">
                {/* Warning Banner */}
                <div className="flex items-center gap-3 p-4 bg-[#FFAE6614] rounded-[8px] mx-[40px]">
                  <InfoSquareIcon size={24} color="#FFAE66" fillOpacity={0.7} />
                  <p className="text-[#FFAE66B2] font-inter text-sm font-medium">
                    Select only the tools relevant to your use case. Adding too
                    many tools can reduce performance.
                  </p>
                </div>

                {/* Specialized Tools Section */}
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FourDashIcon size={24} color="#FFFFFF" />
                      <h2 className=" font-medium text-[#FFFFFF4D]">
                        Specialized Tools
                      </h2>
                    </div>
                    {/* <button
                      type="button"
                      className="flex items-center gap-2 text-[#FFFFFF4D] transition-colors"
                    >
                      <span className="font-['Inter'] font-medium">
                        Request a New Tool
                      </span>
                      <img
                        src={ExternalSVG}
                        alt="External"
                        className="w-5 h-5"
                      />
                    </button> */}
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {isLoadingTools ? (
                      <ToolsListSkeleton count={5} />
                    ) : toolsError ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-red-400">
                          Error loading tools. Using default tools.
                        </div>
                      </div>
                    ) : agentToolsData?.data &&
                      Array.isArray(agentToolsData.data) &&
                      agentToolsData.data.length > 0 ? (
                      // Show API tools - only non-necessary tools in this section
                      agentToolsData.data
                        .filter((tool) => !tool.necessary_tool)
                        .map((tool) => (
                          <ToolItem
                            key={tool.name}
                            tool={tool}
                            isSelected={selectedTools.includes(tool.name)}
                            onCheckedChange={(checked) =>
                              handleToolCheckedChange(tool.name, checked)
                            }
                            isNecessary={false}
                            onViewDetails={handleToolViewDetails}
                          />
                        ))
                    ) : (
                      // Show message when no tools are available
                      <div className="py-8 text-center">
                        <p className="text-[#8A8F98]">
                          No optional tools available
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Default Tools Section */}
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center gap-2">
                    <SettingIcon
                      size={24}
                      color="#FFFFFF"
                      className="opacity-20"
                    />
                    <h2 className=" font-medium text-[#FFFFFF4D]">
                      Default Tools{" "}
                    </h2>
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {agentToolsData?.data &&
                    Array.isArray(agentToolsData.data) &&
                    agentToolsData.data.filter((tool) => tool.necessary_tool)
                      .length > 0 ? (
                      // Show necessary tools from API
                      agentToolsData.data
                        .filter((tool) => tool.necessary_tool)
                        .map((tool) => (
                          <ToolItem
                            key={tool.name}
                            tool={tool}
                            isSelected={true} // Always selected for necessary tools
                            onCheckedChange={undefined} // No toggle for necessary tools
                            isNecessary={true}
                            onViewDetails={handleToolViewDetails}
                          />
                        ))
                    ) : (
                      <div className="py-8 text-center">
                        <p className="text-[#8A8F98]">
                          No Default tools available
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : currentStep === 3 ? (
              // Step 3: Sub-agents Selection
              <div className="space-y-[40px] pb-[40px]">
                {/* Info Banner */}
                <div className="flex items-center gap-3 p-4 bg-[#222224] rounded-[9px] mx-[40px]">
                  <InfoSquareIcon size={24} color="#808080" fillOpacity={1} />
                  <p className="text-[#808080] text-sm font-inter font-medium">
                    Subagents handle specialized tasks. Create a new subagent or
                    select from existing ones.
                  </p>
                </div>

                {/* Your Subagents Section */}
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FourDashIcon size={24} color="#FFFFFF" />
                      <h2 className=" font-medium text-[#FFFFFF4D]">
                        Your Subagents
                      </h2>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        title="Refresh"
                        type="button"
                        onClick={handleRefreshSubagents}
                        disabled={isLoadingSubagents}
                        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-[#FFFFFF4D] rounded-lg hover:bg-[#FFFFFF10] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <RefreshCw
                          className={`w-4 h-4 ${
                            isLoadingSubagents ? "animate-spin" : ""
                          }`}
                        />
                      </button>

                      {userSubagentsData?.user_prompts &&
                        userSubagentsData?.user_prompts.length > 0 && (
                          <button
                            type="button"
                            onClick={handleCreateSubagent}
                            className="bg-[#DD99FF0D] font-medium max-h-[40px] text-[#DD99FF] flex max-md:text-[13px] items-center gap-2 p-2 pr-3 rounded-[10px]"
                          >
                            <PlusIcon size={24} color="#DD99FF" />
                            Add New Subagent
                          </button>
                        )}
                    </div>
                  </div>

                  {/* User Created Subagents */}
                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {isLoadingSubagents ? (
                      <SubAgentsListSkeleton count={3} />
                    ) : subagentsError ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-red-400">
                          Error loading subagents. Please try refreshing.
                        </div>
                      </div>
                    ) : userSubagentsData?.user_prompts &&
                      userSubagentsData.user_prompts.length > 0 ? (
                      // Show user created subagents
                      userSubagentsData.user_prompts.map((subagent) => (
                        <SubAgentItem
                          key={subagent.id}
                          subAgent={{ name: subagent.prompt_name, skill_description: subagent.skill_description }}
                          isSelected={selectedSubAgents.includes(
                            subagent.prompt_name
                          )}
                          onCheckedChange={(checked) =>
                            handleSubAgentCheckedChange(
                              subagent.prompt_name,
                              checked
                            )
                          }
                          isApiSubAgent={true}
                          isDefault={false}
                          onViewDetails={handleSubagentViewDetails}
                          onEdit={handleEditSubagent}
                        />
                      ))
                    ) : (
                      <div
                        className="p-2 border-2 border-[#444] rounded-[16px]  border-dashed border-[#DD99FF33] transition-colors cursor-pointer hover:border-[#DD99FF50]"
                        onClick={handleCreateSubagent}
                      >
                        <div className="flex flex-col rounded-[12px] bg-[#DD99FF05] p-6 items-center gap-[10px] text-center">
                          <h3 className="flex items-center gap-2 font-medium text-[#DD99FF]">
                            <PlusIcon size={24} color="#DD99FF" />
                            Create a New Subagent
                          </h3>
                          <p className="text-[#DD99FF]/50 text-[14px] font-medium font-['Inter']">
                            Your agent will be able to delegate tasks to these
                            subagents
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Default Subagents Section */}
                <div className="flex flex-col gap-3 px-[40px]">
                  <div className="flex items-center gap-2">
                    <CircleDashedIcon size={24} color="#FFFFFF" />
                    <h2 className=" font-medium text-[#FFFFFF4D]">
                      Emergent Subagents
                    </h2>
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {configLoading? (
                      <SubAgentsListSkeleton count={4} />
                    ) : config?.agent_names &&
                      basePromptsData?.prompts &&
                      basePromptsData.prompts.length > 0 ? (
                      // Show dynamic sub-agents from config
                      (() => {
                        // Find the main agent prompt (contains "main" in prompt_name)
                        const mainPrompt = basePromptsData.prompts.find(
                          (prompt) =>
                            prompt.prompt_name.toLowerCase().includes("main")
                        );
                        const selectedPrompt =
                          mainPrompt || basePromptsData.prompts[0];
                        const agentName = selectedPrompt.prompt_name;
                        const agentConfig = config.agent_names.find(
                          (agent: any) => agent.name === agentName
                        );

                        if (agentConfig?.sub_agents) {
                          return agentConfig.sub_agents.map(
                            (subAgentName: string) => {
                              // Find skill information from config based on subAgentName
                              const skillItem = config?.skills_description?.find(
                                (item: any) => item[subAgentName]
                              );
                              const skillInfo = skillItem?.[subAgentName];

                              // Use short_desc as skill_description, fallback to description or default
                              const skillDescription = skillInfo?.short_desc || skillInfo?.description || "Emergent sub-agent";

                              return (
                                <SubAgentItem
                                  key={subAgentName}
                                  subAgent={{
                                    name: skillInfo?.display_name || subAgentName,
                                    description: skillInfo?.description || "Emergent sub-agent",
                                    skill_description: skillDescription
                                  }}
                                  isSelected={selectedSubAgents.includes(subAgentName)}
                                  onCheckedChange={(checked) =>
                                    handleSubAgentCheckedChange(subAgentName, checked)
                                  }
                                  isApiSubAgent={true}
                                  isDefault={true} // Mark as default sub-agent
                                  onViewDetails={handleSubagentViewDetails}
                                />
                              );
                            }
                          );
                        }

                        return (
                          <div className="flex items-center justify-center py-8">
                            <div className="text-[#8A8F98]">
                              No sub-agents found for this agent.
                            </div>
                          </div>
                        );
                      })()
                    ) : (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-[#8A8F98]">
                          No sub-agents found for this agent.
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : currentStep === 4 ? (
              // Step 4: Review
              <div className="space-y-[40px] px-[40px] pb-[40px]">
                {/* Main Agent Info Section */}

                <div className="space-y-[10px]">
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <InfoSquareIcon size={24} color="#FFFFFF" />
                        <h2 className="font-medium text-[#FFFFFF4D]">
                          Main Agent Info
                        </h2>
                      </div>
                      <EditButton onClick={() => setCurrentStep(1)}>
                        Edit
                      </EditButton>
                    </div>
                  </div>
                  <div className="flex flex-col rounded-[16px] w-full gap-6 py-6 px-8 border border-[#222224]">
                    <div className="flex items-center justify-between">
                      <div className="font-medium text-[20px] h-[56px] max-h-[56px] flex items-center text-white">
                        {isEditMode ? prettifyName(agentName || "My Agent") : (agentName || "My Agent")}
                      </div>
                      <div className="max-h-[56px] h-[56px] max-w-[56px] w-[56px] flex items-center justify-center rounded-full bg-[#FFFFFF0D]">
                        <img
                          src={selectedIcon.icon}
                          alt={selectedIcon.name}
                          className="w-7 h-7"
                        />
                      </div>
                    </div>

                    {Object.keys(dynamicVariableValues).length > 0 && (
                      <>
                        {Object.entries(dynamicVariableValues).map(
                          ([key, value]) => (
                            <SystemConstraintSection
                              key={key}
                              section={{
                                id: `review-${key}`,
                                title: key
                                  .replace(/_/g, " ")
                                  .replace(/\b\w/g, (l: string) => l.toUpperCase()),
                                helpText: `Learn about ${key.toLowerCase()}`,
                                readOnly: true,
                                showLock: false,
                                isDynamic: false,
                              }}
                              value={value || "No value set"}
                              onChange={() => {}} // Read-only in review step
                            />
                          )
                        )}
                      </>
                    )}
                  </div>
                </div>

                {/* Selected Tools Section */}
                <div className="space-y-[10px]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FourDashIcon size={24} color="#FFFFFF" />
                      <h2 className=" font-medium text-[#FFFFFF4D]">
                        Selected Tools
                      </h2>
                    </div>
                    <EditButton onClick={() => setCurrentStep(2)}>
                      Edit
                    </EditButton>
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {selectedTools.length > 0 ? (
                      <div className="space-y-3">
                        {selectedTools.map((toolName) => {
                          // Find the tool details from API data
                          const apiTool = agentToolsData?.data?.find(
                            (t) => t.name === toolName
                          );

                          return (
                            <ToolItem
                              key={toolName}
                              tool={apiTool || { name: toolName }}
                              isSelected={true}
                              hideToggle={true}
                              onViewDetails={handleToolViewDetails}
                            />
                          );
                        })}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8">
                        <p className="text-[#8A8F98] text-sm">
                          No tools selected
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Selected Sub-agents Section */}
                <div className="flex flex-col gap-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <SettingIcon size={24} color="#FFFFFF" className="opacity-20" />
                      <h2 className=" font-medium text-[#FFFFFF4D]">
                        Selected Sub-agents
                      </h2>
                    </div>
                    <EditButton onClick={() => setCurrentStep(3)}>
                      Edit
                    </EditButton>
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {selectedSubAgents.length > 0 ? (
                      <div className="space-y-3">
                        {selectedSubAgents.map((subAgentName) => {
                          // Find the sub-agent details from user data, config data, or fallback to static data
                          const userSubAgent =
                            userSubagentsData?.user_prompts?.find(
                              (s) => s.prompt_name === subAgentName
                            );
                          const staticSubAgent = DEFAULT_SUBAGENTS.find(
                            (s) => s.name === subAgentName
                          );

                          // Check if this is a default sub-agent from config
                          const isDefaultSubAgent =
                            config?.agent_names &&
                            basePromptsData?.prompts &&
                            basePromptsData.prompts.length > 0 &&
                            (() => {
                              // Find the main agent prompt (contains "main" in prompt_name)
                              const mainPrompt = basePromptsData.prompts.find(
                                (prompt) =>
                                  prompt.prompt_name
                                    .toLowerCase()
                                    .includes("main")
                              );
                              const selectedPrompt =
                                mainPrompt || basePromptsData.prompts[0];
                              const agentName = selectedPrompt.prompt_name;
                              const agentConfig = config.agent_names.find(
                                (agent: any) => agent.name === agentName
                              );
                              return agentConfig?.sub_agents?.includes(
                                subAgentName
                              );
                            })();

                          // Find skill information from config based on subAgentName
                          const skillItem = config?.skills_description?.find(
                            (item: any) => item[subAgentName]
                          );
                          const skillInfo = skillItem?.[subAgentName];

                          // Use short_desc as skill_description, fallback to description or default
                          const skillDescription = skillInfo?.short_desc || skillInfo?.description || "Emergent sub-agent";

                          const subAgentData = {
                            name: skillInfo?.display_name || subAgentName,
                            description: userSubAgent
                              ? userSubAgent.description || "Custom sub-agent"
                              : isDefaultSubAgent
                              ? skillInfo?.description || "Default sub-agent"
                              : staticSubAgent?.description || "Sub-agent description",
                            skill_description: userSubAgent?.skill_description || skillDescription
                          };

                          return (
                            <SubAgentItem
                              key={subAgentName}
                              subAgent={subAgentData}
                              isSelected={true}
                              isApiSubAgent={isDefaultSubAgent}
                              isDefault={false}
                              hideToggle={true}
                              onViewDetails={handleSubagentViewDetails}
                            />
                          );
                        })}
                      </div>
                    ) : (
                      <div className="flex items-center justify-center py-8">
                        <p className="text-[#8A8F98] text-sm">
                          No sub-agents selected
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : null}
          </div>

          {/* Fixed Footer */}
          <div className="absolute bottom-0 left-0 right-0 border-t-[1px] rounded-b-[8px] z-[99] py-7 px-6 h-[105px] bg-[#1A1A1C] border-[#262629]">
            <div className="flex items-center justify-between">
              {currentStep > 1 ? (
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(currentStep - 1);
                    // Reset scroll position when going back
                    if (scrollableAreaRef.current) {
                      scrollableAreaRef.current.scrollTop = 0;
                    }
                  }}
                  className="flex items-center gap-2 px-6 py-3 font-semibold text-[#939399] bg-[#222224] hover:bg-[#FFFFFF10] rounded-full transition-colors"
                >
                  <img src={BackGray} alt="Previous" className="w-5 h-5" />
                  Previous Step
                </button>
              ) : (
                <div></div>
              )}

              <button
                type="button"
                onClick={handleContinue}
                disabled={isCreatingPrompt || isUpdatingPrompt || !canProceed()}
                className={cn("flex items-center gap-2 px-6 py-3 font-semibold text-black bg-white rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed", {
                  "bg-[#5FE55C] hover:bg-[#5FE55C90]": currentStep === 4,
                }
                )}
              >
                {(isCreatingPrompt || isUpdatingPrompt) && currentStep === 4 ? (
                  <>
                    {isEditMode ? "Updating Agent..." : "Creating Agent..."}
                    <div className="w-4 h-4 border-2 border-black rounded-full border-t-transparent animate-spin"></div>
                  </>
                ) : (
                  <>
                    {currentStep === 4 && isEditMode
                      ? "Update Your Agent"
                      : BUTTON_TEXTS[currentStep as keyof typeof BUTTON_TEXTS]}
                    <img src={NextArrow} alt="Next" className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Icon Selection Modal */}
      <IconSelectionModal />

      {/* Details Modal */}
      <DetailsModal />

      {/* Learn More Modal */}
      <LearnMoreModal
        isOpen={isLearnMoreModalOpen}
        onOpenChange={setIsLearnMoreModalOpen}
        currentStep={currentStep}
      />


    </section>
  );
};
