import React, { useRef, useEffect, useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { AgentTool } from "@/store/api/promodeApiSlice";
import { CustomSwitchUniversal } from "@/components/ui/custom-switch-universal";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

import LockSVG from "@/assets/pro/lock.svg";
import WorkBenchIcon from "@/assets/pro/workbench.svg";

import NormalRobot from "@/components/icons/Robots/NormalRobot";
import { CustomSwitchNamed } from "@/components/ui/custom-switch-named";
import EditIcon from "@/components/icons/Actions/EditIcon";
import { X } from "lucide-react";
import ExpandIcon from "@/components/icons/Actions/ExpandIcon";
import LockIcon from "@/components/icons/LockIcon";

// Types
export interface InstructionItem {
  text: string;
  highlight?: string;
  boldText?: string;
}

export interface SystemConstraintSectionType {
  id: string;
  title: string;
  helpText: string;
  readOnly: boolean;
  showLock: boolean;
  isDynamic?: boolean;
  variableName?: string;
}

// Instruction Components
export const InstructionItem: React.FC<{ item: InstructionItem }> = ({
  item,
}) => (
  <p className="items-start gap-2 font-['Inter'] text-[14px]">
    <span className='font-["Inter"] mr-1'>→</span>
   {item.boldText &&  <span className="font-['Inter'] text-[14px] font-medium text-nowrap">{item.boldText}</span>}
    {item.text}
      {item.highlight && (
        <span className="text-[#80AAFF] font-['Inter']">{item.highlight}</span>
      )}
  </p>
);

export const InstructionList: React.FC<{ items: InstructionItem[] }> = ({
  items,
}) => (
  <div className="space-y-4 text-[#737780] font-medium font-['Inter'] mb-4">
    {items.map((item, index) => (
      <InstructionItem key={index} item={item} />
    ))}
  </div>
);

// Edit System Prompt Modal Component
export const EditSystemPromptModal: React.FC<{
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  value: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
}> = ({ isOpen, onOpenChange, title, value, onChange, readOnly = false }) => {
  const [editedValue, setEditedValue] = useState(value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Update local state when value prop changes
  useEffect(() => {
    setEditedValue(value);
  }, [value]);

  const handleSave = () => {
    if (!readOnly) {
      onChange(editedValue);
    }
    onOpenChange(false);
  };

  const handleCancel = () => {
    setEditedValue(value); // Reset to original value
    onOpenChange(false);
  };

  // Focus textarea when modal opens (only if not read-only)
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea && isOpen && !readOnly) {
      setTimeout(() => {
        textarea.focus();
      }, 100);
    }
  }, [isOpen, readOnly]);


  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[95dvh] bg-[#18181A] text-white border border-[#242424] rounded-[16px] overflow-hidden flex flex-col">
        <DialogHeader className="flex flex-row bg-[#1C1C1F] items-center justify-between pb-4 space-y-0">
          <DialogTitle className="text-[#C4C4CC] font-brockmann text-[20px] font-medium leading-[28px] tracking-[-0.44px]">
            {title}
          </DialogTitle>
          <button
            type="button"
            onClick={handleCancel}
            className="rounded-[8px] opacity-70 h-[36px] w-[36px]  bg-[#FFFFFF0A] hover:bg-[#FFFFFF10] ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground flex items-center justify-center"
          >
            <X className="w-5 h-5" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>

        <div className="flex flex-col flex-1 gap-4 overflow-hidden bg-[#18181A]">
          <Textarea
            ref={textareaRef}
            value={editedValue}
            onChange={(e) => setEditedValue(e.target.value)}
            readOnly={readOnly}
            tabIndex={readOnly ? -1 : 0}
            onFocus={(e) => {
              if (readOnly) {
                e.target.blur();
              }
            }}
            className={cn(
              "font-['Inter'] border-none bg-none p-6 bg-[#18181A] text-white text-[16px] min-h-[600px] resize-none placeholder:text-[#C4C4CC] focus:border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 overflow-y-auto flex-1",
              readOnly && "cursor-not-allowed opacity-60"
            )}
            placeholder="Enter your system prompt..."
          />
        </div>
        <div className="flex justify-end gap-3 p-6 border-t-[1px] border-[#242424]">
            <button
              onClick={handleCancel}
              className="border-[#333] px-5 py-3 font-medium text-[#C4C4CC] bg-[#1D1D1E] rounded-full hover:bg-[#1D1D1E] hover:text-white"
            >
              Cancel
            </button>
            {!readOnly && (
              <button
                onClick={handleSave}
                className="bg-[#2EE572] px-5 py-3 text-[#0E0E0F] rounded-full font-semibold hover:bg-[#2EE572]/90"
              >
                Save & Continue
              </button>
            )}
          </div>
      </DialogContent>
    </Dialog>
  );
};

// System Constraint Section Component
export const SystemConstraintSection: React.FC<{
  section: SystemConstraintSectionType;
  value: string;
  onChange: (value: string) => void;
  dynamicVariableValues?: Record<string, string>;
  onDynamicVariableChange?: (variableName: string, value: string) => void;
}> = ({
  section,
  value,
  onChange,
  dynamicVariableValues,
  onDynamicVariableChange,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // For dynamic sections, use the dynamic variable value instead of the shared value
  const currentValue =
    section.isDynamic && section.variableName
      ? dynamicVariableValues?.[section.variableName] || ""
      : value;

  const handleChange = (newValue: string) => {
    if (section.isDynamic && section.variableName && onDynamicVariableChange) {
      onDynamicVariableChange(section.variableName, newValue);
    } else {
      onChange(newValue);
    }
  };

  const handleExpandWithCursor = (clickEvent: React.MouseEvent) => {
    setIsExpanded(true);

    // Focus the textarea and set cursor position based on click location
    setTimeout(() => {
      const textarea = textareaRef.current;
      if (textarea) {
        textarea.focus();

        // Calculate approximate cursor position based on click coordinates
        const rect = textarea.getBoundingClientRect();
        const clickX = clickEvent.clientX - rect.left - 16; // Account for padding
        const clickY = clickEvent.clientY - rect.top - 16; // Account for padding

        // Get textarea styles to calculate character dimensions
        const computedStyle = window.getComputedStyle(textarea);
        const fontSize = parseFloat(computedStyle.fontSize);
        const lineHeight = parseFloat(computedStyle.lineHeight) || fontSize * 1.2;

        // Estimate character width (approximate for monospace-like fonts)
        const charWidth = fontSize * 0.6;

        // Calculate approximate line and character position
        const lineIndex = Math.floor(clickY / lineHeight);
        const charIndex = Math.floor(clickX / charWidth);

        // Split text into lines and find the cursor position
        const lines = currentValue.split('\n');
        let position = 0;

        for (let i = 0; i < Math.min(lineIndex, lines.length - 1); i++) {
          position += lines[i].length + 1; // +1 for newline character
        }

        if (lineIndex < lines.length) {
          position += Math.min(charIndex, lines[lineIndex]?.length || 0);
        }

        // Set the cursor position
        textarea.setSelectionRange(position, position);
      }
    }, 0);
  };

  const handleExpand = () => {
    // For system constraints, open modal instead of expanding
    if (section.title.includes("System Constraints") || section.title.includes("System Prompt")) {
      setIsModalOpen(true);
    } else {
      setIsExpanded(true);
      // Focus the textarea when expanding without click position (only if not locked)
      setTimeout(() => {
        if (!section.showLock) {
          textareaRef.current?.focus();
        }
      }, 0);
    }
  };

  // Auto-resize textarea based on content and expanded state
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';

      if (isExpanded) {
        // When expanded, use content-based height with max 500px
        const newHeight = Math.min(textarea.scrollHeight, 500);
        textarea.style.height = `${newHeight}px`;
      } else {
        // When collapsed, use fixed 150px height
        textarea.style.height = '300px';
      }
    }
  }, [currentValue, isExpanded]);

  return (
    <>
      <EditSystemPromptModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        title={
          section.readOnly || section.showLock
            ? section.title
            : `Edit ${section.title}`
        }
        value={currentValue}
        onChange={handleChange}
        readOnly={section.readOnly || section.showLock}
      />

      <div className="flex flex-col gap-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Label
              htmlFor={section.id}
              className="font-medium text-[14px] text-[#e4eeff] font-inter"
            >
              {section.title}
            </Label>
            {section.showLock && (
              <span className="flex items-center font-inter font-medium text-[14px] gap-1 text-white/50">
                <LockIcon size={16} />
                read only
              </span>
            )}
          </div>
          {/* <div className="flex items-center gap-2">
            <button
              type="button"
              className="text-[#4D4D4D] items-center font-medium font-['Inter'] transition-colors flex gap-1"
            >
              <span className="text-[14px] font-['Inter']">
                {section.helpText}
              </span>
              <img src={ExternalSVG} alt="External" className="w-5 h-5" />
            </button>
          </div> */}
        </div>
      <div className="relative rounded-lg overflow-clip">
        <Textarea
          ref={textareaRef}
          id={section.id}
          value={currentValue}
          onChange={(e) => handleChange(e.target.value)}
          readOnly={section.readOnly || section.showLock}
          tabIndex={section.showLock ? -1 : 0}
          onFocus={(e) => {
            if (section.showLock) {
              e.target.blur();
            }
          }}
          className={cn(
            "bg-[#FFFFFF05] font-['Inter'] rounded-lg border border-[#262629] p-4 text-white text-[16px] max-h-[300px] resize-none placeholder:text-[#C4C4CC] focus:border-[#666] focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0",
            section.showLock && "opacity-60"
          )}
        />
        {/* Expand icon - opens modal for system constraints, expands for others */}
        <div
          onClick={() => {
            console.log('Expand icon clicked for section:', section.title);
            if (section.title.includes("System Constraints") || section.title.includes("System Prompt")) {
              console.log('Opening modal for system constraints/prompt');
              setIsModalOpen(true);
            } else {
              if (isExpanded) {
                setIsExpanded(false);
              } else {
                handleExpand();
              }
            }
          }}
          className="absolute hover:bg-[#FFFFFF]/20 bg-[#FFFFFF1A] right-2 bottom-2 p-2 cursor-pointer z-[20] rounded-[8px] backdrop-blur-lg"
        >
          <ExpandIcon
            size={16}
            className={cn(
              " text-white transition-transform duration-200",
              (section.title.includes("System Constraints") || section.title.includes("System Prompt")) ? "rotate-0" : (isExpanded ? "rotate-180" : "rotate-0")
            )}
          />
        </div>
      </div>
    </div>
    </>
  );
};

// Tool Item Component
export const ToolItem: React.FC<{
  tool:
    | AgentTool
    | {
        id?: string;
        name: string;
        description?: string;
        icon?: string;
        user_descriptions?: {
          short_desc: string;
          description: string;
        };
        status?: boolean;
        necessary_tool?: boolean;
      };
  isSelected?: boolean;
  onToggle?: () => void;
  onCheckedChange?: (checked: boolean) => void;
  isNecessary?: boolean;
  variant?: "agent" | "subagent";
  onViewDetails?: (tool: any) => void;
  hideToggle?: boolean;
}> = ({
  tool,
  isSelected = false,
  onToggle,
  onCheckedChange,
  isNecessary = false,
  variant = "agent",
  onViewDetails,
  hideToggle = false,
}) => {
  const isApiTool = "user_descriptions" in tool;
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={cn(
        "flex items-center group gap-[4rem] justify-between p-4 rounded-lg cursor-pointer  bg-[#18181A] transition-all ease-in-out duration-200 hover:bg-[#FFFFFF]/5",
      )}
      onClick={()=>{ onViewDetails?.(tool);}}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex items-center gap-3">
        <div
          className="flex items-center justify-center min-w-10 min-h-10 bg-[#272729] rounded-full"
        >
          <img src={WorkBenchIcon} alt="Workbench" className="w-6 h-6" />
        </div>
        <div>
          <h3 className="font-medium text-[#ced7e5] capitalize">
            {tool.name.replaceAll("_", " ")}
          </h3>
          <div className="flex items-center gap-2">
            <p className="text-[#626366] text-[14px] font-medium font-['Inter'] text-nowrap truncate">
              {(tool as AgentTool).user_descriptions?.short_desc}
            </p>
            <AnimatePresence>
              {isHovered && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2, ease: "easeInOut" }}
                  className="w-1 h-1 rounded-full bg-[#45474D]"
                />
              )}
            </AnimatePresence>
            <AnimatePresence>
              {isHovered && (
                <motion.span
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -10 }}
                  transition={{ duration: 0.2, ease: "easeInOut" }}
                  className="text-[#B7BFCC] text-nowrap text-[14px] font-medium font-['Inter'] cursor-pointer hover:text-white"
                >
                  View Details
                </motion.span>
              )}
            </AnimatePresence>
          </div>
        </div>
      </div>
      {!hideToggle && (
        <CustomSwitchUniversal
          checked={isSelected}
          onCheckedChange={(checked) => {
            if (!isNecessary) {
              if (onCheckedChange) {
                onCheckedChange(checked);
              } else if (onToggle) {
                onToggle();
              }
            }
          }}
          disabled={isNecessary}
        />
      )}
    </div>
  );
};

// Sub Agent Item Component
export const SubAgentItem: React.FC<{
  subAgent: {
    id?: string;
    name: string;
    description?: string;
    icon?: string;
    skill_description?: string;
  };
  isSelected?: boolean;
  onToggle?: () => void;
  onCheckedChange?: (checked: boolean) => void;
  isApiSubAgent?: boolean;
  isDefault?: boolean;
  onViewDetails?: (subAgent: any) => void;
  onEdit?: (subAgent: any) => void;
  hideToggle?: boolean;
}> = ({
  subAgent,
  isSelected = false,
  onToggle,
  onCheckedChange,
  isApiSubAgent = false,
  isDefault = false,
  onViewDetails,
  onEdit,
  hideToggle = false,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <div
      className={cn(
        "flex items-center group justify-between p-4 bg-[#18181A] cursor-pointer hover:bg-[#FFFFFF05] rounded-lg transition-all ease-in-out duration-200",
      )}
      onClick={()=>{
         onViewDetails?.({ ...subAgent, isApiSubAgent, isDefault });
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
    <div className="flex items-center gap-3">
      <div
        className="flex items-center justify-center w-10 h-10 rounded-full bg-[#272729]"
      >
        <NormalRobot size={20} primaryColor={isDefault ? "#fff" : "#dd99ff"} />
      </div>
      <div>
        <h3 className="font-medium  text-[#ced7e5] capitalize group-hover:text-[#E5EFFF]">
          {subAgent.name.replaceAll("_", " ")}
        </h3>
        <div className="flex items-center gap-2">
          <p className="text-[#626366] text-[14px] font-medium font-['Inter']">
            {subAgent.skill_description || subAgent.description || "No description available"}
          </p>
          <AnimatePresence>
            {isHovered && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="w-1 h-1 rounded-full bg-[#45474D]"
              />
            )}
          </AnimatePresence>
          <AnimatePresence>
            {isHovered && (
              <motion.span
                initial={{ opacity: 0, x: -10 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -10 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="text-[#B7BFCC] text-[14px] font-medium font-['Inter'] cursor-pointer hover:text-white"
              >
                View Details
              </motion.span>
            )}
          </AnimatePresence>
        </div>
      </div>
    </div>
    <div className="flex items-center gap-3">
      {!isDefault && onEdit && (
        <button
          type="button"
          className="flex items-center font-medium gap-1 p-[6px] pl-3 pr-4 rounded-full text-[#C4C4CC] bg-[#FFFFFF0A] hover:bg-[#FFFFFF10] transition-colors duration-150 group"
          onClick={(e) => {
            e.stopPropagation();
            onEdit(subAgent);
          }}
        >
          <EditIcon width={20} height={20} fill="#C4C4CC" />
          Edit
        </button>
      )}
      {!hideToggle && (
        <CustomSwitchNamed
          checked={isSelected}
          checkedColor={!isDefault ? "#DD99FF" : "#29BCCC"}
          onCheckedChange={(checked) => {
            if (onCheckedChange) {
              onCheckedChange(checked);
            } else if (onToggle) {
              onToggle();
            }
          }}
          disabled={false}
        />
      )}
    </div>
  </div>
  );
};
