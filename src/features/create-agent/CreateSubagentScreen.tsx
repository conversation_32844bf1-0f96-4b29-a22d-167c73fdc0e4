import React, { useState, useEffect, useCallback, useRef } from "react";
import { motion } from "framer-motion";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { useTabState } from "@/components/TabBar";
import { useToast } from "@/hooks/use-toast";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCreateSubagentFormData,
  setCreateSubagentFormData,
  clearCreateSubagentFormData,
  CreateSubagentFormData
} from "@/store/tabSlice";
import type { RootState } from "@/store";
// useConfig removed as subagents don't need config for sub-agents
import {
  useLazyGetBasePromptsQuery,
  useLazyGetAgentToolsQuery,
  useCreateUserPromptMutation,
  useUpdateUserPromptMutation,
} from "@/store/api/promodeApiSlice";
import {
  InstructionList,
  SystemConstraintSection,
  ToolItem,
} from "./components/SharedComponents";
import {
  SystemConstraintsSkeleton,
  ToolsListSkeleton,
} from "./components/SkeletonComponents";

import WorkBenchIcon from "@/assets/pro/workbench.svg";
import ExternalSVG from "@/assets/pro/external.svg";
import InfoSquareIcon from "@/components/icons/InfoSquareIcon";
import { LearnMoreButton } from "@/components/ui/learn-more-button";

import ControlsIcon from "@/assets/controls.svg";
import BackgroundSVG from "@/assets/pro/bg.svg"
import { DetailsModal as DetailsModalComponent } from "./components/DetailsModal";
import { LearnMoreModal } from "./components/LearnMoreModal";


import NextArrow from "@/assets/pro/nextArrow.svg";
import BackGray from "@/assets/pro/back-gray.svg";
import FourDashIcon from "@/components/icons/FourDash";
import TextEditIcon from "@/components/icons/TextEdit";
import SettingIcon from "@/components/icons/SettingIcon";
// Robot imports removed as they're not used in subagent creation

import { cn } from "@/lib/utils";
import useScreenSize from "@/hooks/useScreenSize";

// Constants - Only 2 steps for subagent creation
const STEP_CONFIG = {
  1: {
    title: "Define your Sub-Agent",
    description:
      "Sub-agents are your agent's specialist consultants—highly focused AI helpers that excel in specific domains.",
    icon: WorkBenchIcon,
    iconAlt: "Workbench",
    welcomeTitle: "Creating a Sub-Agent on Emergent",
    learnMoreText: "Learn about subagent design",
    tips:"The clearer you are, the better your Sub Agent performs."
  },
  2: {
    title: "Select Tools",
    description:
      "Equip your agent with the right capabilities: Tools are essential for your Sub Agent to operate and work. Please choose the specialized tools required by your Agent as per your needs and goals. ",
    icon: ControlsIcon,
    iconAlt: "Tools",
    welcomeTitle: "What exactly are tools?",
    learnMoreText: "Learn about tools",
    tips:"The tools selected by default are mandatorily required by the agents to function."
  },
};

const SUB_AGENT_INSTRUCTION_ITEMS = [
  {
    text: "Name your sub-agent based on its specialty and role",
  },
  {
    text: "Give your subagent a definition, based on when to call and why to call. The main agent uses subagent definition to call subagent, and determine if their expertise is needed.",
  },
  {
    text: "Map their workflow, define the step-by-step approach to approach the required task",
  },
  {
    text: "Include guidelines, best practices, constraints, and things to avoid",
  },
];

const TOOLS_INSTRUCTION_ITEMS = [
  {
    text: "Each tool serves a specific function in your agent's workflow, choose them wisely",
  },
  {
    text: "Include only essential tools to keep your agent focused and contextually aware, do not overload with too many tools",
  },
  {
    text: "Choose tools that directly support your agent's core objectives",
  },
];



// DEFAULT_SUBAGENTS removed as subagents don't have sub-agents

const SYSTEM_CONSTRAINT_SECTIONS = [
  {
    id: "system-constraints-1",
    title: "System Constraints",
    helpText: "What are system constraints",
    readOnly: true,
    showLock: true,
  },
  {
    id: "system-constraints-2",
    title: "System Constraints 2",
    helpText: "What are system constraints",
    readOnly: true,
    showLock: true,
  },
  {
    id: "system-prompt",
    title: "System Prompt",
    helpText: "Learn about system prompts",
    readOnly: true,
    showLock: false,
  },
];

const BUTTON_TEXTS = {
  1: "Continue to Add Tools",
  2: "Save Your Subagent",
};

const EDIT_BUTTON_TEXTS = {
  1: "Continue to Add Tools",
  2: "Update Your Subagent",
};

interface CreateSubagentScreenProps {
  tabId: string;
}



export const CreateSubagentScreen: React.FC<CreateSubagentScreenProps> = ({
  tabId,
}) => {
  // Redux hooks for form persistence
  const dispatch = useDispatch();
  const persistedFormData = useSelector((state: RootState) =>
    selectCreateSubagentFormData(state, tabId)
  );

  const [currentStep, setCurrentStep] = useState(1); // Only 2 steps for subagent
  const [subagentName, setSubagentName] = useState("");
  const [subagentGuidelines, setSubagentGuidelines] = useState("");
  const [systemConstraints, setSystemConstraints] = useState(
    "You are a helpful AI assistant. Always follow safety guidelines You are a"
  );

  // Dynamic variables state - stores values for each dynamic variable
  const [dynamicVariableValues, setDynamicVariableValues] = useState<
    Record<string, string>
  >({});

  // Selected tools state
  const [selectedTools, setSelectedTools] = useState<string[]>([]);

  // Details modal state
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);
  const [detailsModalData, setDetailsModalData] = useState<{
    type: 'tool';
    data: any;
  } | null>(null);

  // Learn More modal state
  const [isLearnMoreModalOpen, setIsLearnMoreModalOpen] = useState(false);

  // Ref for scrollable content area
  const scrollableAreaRef = useRef<HTMLDivElement>(null);

  // Subagents don't have sub-agents, so selectedSubAgents state removed

  // Tab management and toast hooks
  const { removeTab, setActiveTab, getActiveTab, getAllTabs } = useTabState();
  const { toast } = useToast();

  // Mobile check
  const { isMobile } = useScreenSize();

  // Get current tab data to check for edit mode
  const currentTab = getActiveTab();
  const isEditMode = currentTab?.state?.editMode || false;
  const agentData = currentTab?.state?.agentData;

  // Helper function to prettify name by replacing underscores with spaces and capitalizing
  const prettifyName = (name: string) => {
    return name.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
  };

  // Config hook removed as subagents don't need config for sub-agents

  // Lazy fetch base prompts from API - only when needed
  const [
    getBasePrompts,
    { data: basePromptsData, isLoading: isLoadingPrompts, error: promptsError },
  ] = useLazyGetBasePromptsQuery();

  // Lazy fetch agent tools - only when needed
  const [
    getAgentTools,
    { data: agentToolsData, isLoading: isLoadingTools, error: toolsError },
  ] = useLazyGetAgentToolsQuery();

  // Create and update user prompt mutations
  const [createUserPrompt, { isLoading: isCreatingPrompt }] =
    useCreateUserPromptMutation();
  const [updateUserPrompt, { isLoading: isUpdatingPrompt }] =
    useUpdateUserPromptMutation();

  // Restore form data from localStorage on component mount (only if form is empty)
  useEffect(() => {
    if (persistedFormData && subagentName === "" && selectedTools.length === 0) {
      // Only restore if form is empty to prevent overwriting user input
      setSubagentName(persistedFormData.subagentName);
      setSubagentGuidelines(persistedFormData.subagentGuidelines);
      setSystemConstraints(persistedFormData.systemConstraints);
      setDynamicVariableValues(persistedFormData.dynamicVariableValues);
      setSelectedTools(persistedFormData.selectedTools);
      setCurrentStep(persistedFormData.currentStep);
    }
  }, [persistedFormData]); // Removed form fields from dependencies

  // Initialize form with existing data in edit mode
  useEffect(() => {
    if (isEditMode && agentData) {
      // In edit mode, use agent data
      setSubagentName(agentData.prompt_name || "");
      // Use skill_description for editing, but keep description as the key
      setSubagentGuidelines(agentData.skill_description || agentData.description || "");
      setSelectedTools(agentData.prompt_tools || []);
      setDynamicVariableValues(agentData.prompt_variables || {});

      // Start at step 2 (review) in edit mode for subagents
      setCurrentStep(2);
    }
  }, [isEditMode, agentData]);

  // Memoized function to save form data to Redux
  const saveFormData = useCallback(() => {
    const formData: CreateSubagentFormData = {
      subagentName,
      subagentGuidelines,
      systemConstraints,
      dynamicVariableValues,
      selectedTools,
      currentStep,
    };
    dispatch(setCreateSubagentFormData({ tabId, formData }));
  }, [
    subagentName,
    subagentGuidelines,
    systemConstraints,
    dynamicVariableValues,
    selectedTools,
    currentStep,
    dispatch,
    tabId,
  ]);

  // Save form data to localStorage with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(saveFormData, 300); // 300ms debounce
    return () => clearTimeout(timeoutId);
  }, [saveFormData]);

  // Reset scroll position when step changes
  useEffect(() => {
    if (scrollableAreaRef.current) {
      scrollableAreaRef.current.scrollTop = 0;
    }
  }, [currentStep]);

  // Fetch base prompts when component mounts
  useEffect(() => {
    getBasePrompts();
  }, [getBasePrompts]);

  // Initialize dynamic variable values with default values when base prompts data is loaded
  useEffect(() => {
    if (basePromptsData?.prompts && basePromptsData.prompts.length > 0) {
      const subagentPrompt = getSubagentPrompt();
      if (subagentPrompt) {
        const defaultValues = subagentPrompt.default_variable_values || {};

        // Only set default values if dynamicVariableValues is empty (first load)
        if (Object.keys(dynamicVariableValues).length === 0) {
          setDynamicVariableValues(defaultValues);
        }
      }
    }
  }, [basePromptsData, dynamicVariableValues]);

  // Helper function to find the subagent prompt
  const getSubagentPrompt = () => {
    if (!basePromptsData?.prompts || basePromptsData.prompts.length === 0) {
      return null;
    }

    // Find prompt whose prompt_name includes "subagent"
    const subagentPrompt = basePromptsData.prompts.find(prompt =>
      prompt.prompt_name.toLowerCase().includes('subagent')
    );

    // Fallback to first prompt if no subagent prompt found
    return subagentPrompt || basePromptsData.prompts[0];
  };

  // Fetch agent tools when we have base prompts data and user is on step 2
  useEffect(() => {
    if (
      currentStep === 2 &&
      basePromptsData?.prompts &&
      basePromptsData.prompts.length > 0
    ) {
      const subagentPrompt = getSubagentPrompt();
      if (subagentPrompt) {
        const parentPrompt = subagentPrompt.prompt_name; // This is the agent_name for the tools API
        getAgentTools(parentPrompt);
      }
    }
  }, [currentStep, basePromptsData, getAgentTools]);

  // Sub-agents initialization removed as subagents don't have sub-agents

  // Auto-resize textarea when content changes
  useEffect(() => {
    const textarea = document.getElementById('system-prompts-readonly') as HTMLTextAreaElement;
    if (textarea && getSubagentPrompt()?.prompt) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 500) + 'px';
    }
  }, [basePromptsData]);

  // Automatically select all tools when agent tools data is loaded
  useEffect(() => {
    if (agentToolsData?.data && Array.isArray(agentToolsData.data) && agentToolsData.data.length > 0) {
      // Select all tools (both necessary and non-necessary) by default
      const allTools = agentToolsData.data.map((tool) => tool.name);

      if (allTools.length > 0) {
        setSelectedTools((prev) => {
          // Add all tools if they're not already selected
          const newTools = [...prev];
          allTools.forEach((toolName) => {
            if (!newTools.includes(toolName)) {
              newTools.push(toolName);
            }
          });
          return newTools;
        });
      }
    }
  }, [agentToolsData]);

  // Generate dynamic system constraint sections based on API response
  const generateDynamicSections = () => {
    if (!basePromptsData?.prompts || basePromptsData.prompts.length === 0) {
      return SYSTEM_CONSTRAINT_SECTIONS; // Fallback to static sections
    }

    // Get the subagent prompt's dynamic variables
    const subagentPrompt = getSubagentPrompt();
    if (!subagentPrompt) {
      return SYSTEM_CONSTRAINT_SECTIONS; // Fallback to static sections
    }
    const dynamicVariables = subagentPrompt.dynamic_variables || [];

    // Generate sections for each dynamic variable
    const dynamicSections = dynamicVariables.map(
      (variable: string, index: number) => ({
        id: `dynamic-variable-${index}`,
        title: variable
          .replace(/_/g, " ")
          .replace(/\b\w/g, (l: string) => l.toUpperCase()), // Convert snake_case to Title Case
        helpText: `Learn about ${variable.toLowerCase()}`,
        readOnly: false,
        showLock: false,
        isDynamic: true,
        variableName: variable,
      })
    );

    return dynamicSections;
  };

  const systemConstraintSections = generateDynamicSections();

  // Handler for dynamic variable changes
  const handleDynamicVariableChange = (variableName: string, value: string) => {
    setDynamicVariableValues((prev) => ({
      ...prev,
      [variableName]: value,
    }));
  };



  // Handler for tool selection with checked state
  const handleToolCheckedChange = (toolName: string, checked: boolean) => {
    // Check if this is a necessary tool - if so, don't allow toggling
    const tool = agentToolsData?.data && Array.isArray(agentToolsData.data)
      ? agentToolsData.data.find((t) => t.name === toolName)
      : undefined;
    if (tool?.necessary_tool) {
      return; // Don't allow toggling necessary tools
    }

    setSelectedTools((prev) => {
      if (checked) {
        return prev.includes(toolName) ? prev : [...prev, toolName];
      } else {
        return prev.filter((name) => name !== toolName);
      }
    });
  };

  // Handler for sub-agent selection removed as subagents don't have sub-agents

  // Check if we can proceed to the next step or save
  const canProceed = () => {
    if (currentStep === 1) {
      // Step 1: Subagent name and guidelines are required + all dynamic variables must be filled
      if (subagentName.trim().length === 0 || subagentGuidelines.trim().length === 0) {
        return false;
      }

      // Check if all dynamic variables are filled
      const subagentPrompt = getSubagentPrompt();
      if (subagentPrompt?.dynamic_variables) {
        for (const variable of subagentPrompt.dynamic_variables) {
          const value = dynamicVariableValues[variable];
          if (!value || value.trim().length === 0) {
            return false;
          }
        }
      }

      return true;
    }
    if (currentStep === 2) {
      const subagentPrompt = getSubagentPrompt();
      return (
        subagentName.trim().length > 0 &&
        subagentGuidelines.trim().length > 0 &&
        basePromptsData?.prompts &&
        basePromptsData.prompts.length > 0 &&
        subagentPrompt !== null
      ); // Need subagent name, guidelines and subagent base prompt
    }
    return true; // Other steps don't have validation for now
  };

  const handleContinue = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
      // Reset scroll position when moving to next step
      if (scrollableAreaRef.current) {
        scrollableAreaRef.current.scrollTop = 0;
      }
    } else {
      // Complete subagent creation - close tab, activate home, show toast
      handleSaveSubagent();
    }
  };

  const handleSaveSubagent = async () => {
    try {
      // Get the subagent prompt's ID for base_prompt_id
      const subagentPrompt = getSubagentPrompt();
      const basePromptId = subagentPrompt?.id;

      if (
        !basePromptId ||
        !basePromptsData?.prompts ||
        basePromptsData.prompts.length === 0 ||
        !subagentPrompt
      ) {
        toast({
          title: "Error",
          description: "No subagent base prompt available. Please try again.",
          variant: "destructive",
          duration: 5000,
        });
        return;
      }

      // Prepare the request data for subagent
      const requestData = {
        prompt_name: subagentName || "My Subagent",
        description: subagentGuidelines || "", // Pass guidelines as skill_description for subagent definition
        parent_prompt: subagentPrompt.prompt_name, // Dynamic parent prompt from selected subagent prompt
        base_prompt_id: basePromptId,
        prompt_subagents: [], // Subagents don't have sub-agents
        prompt_tools: selectedTools.length > 0 ? selectedTools : null, // Use selected tools or null if none selected
        prompt_variables: dynamicVariableValues,
        system_prompt_injection_templates: [
          "integration_playbook_expert_prompt",
        ], // You can make this dynamic
        is_main_agent: "false", // This is a subagent, not a main agent
      };

      // Make the API call - create or update based on mode
      if (isEditMode && agentData?.id) {
        await updateUserPrompt({
          id: agentData.id,
          data: requestData,
        }).unwrap();
      } else {
        await createUserPrompt(requestData).unwrap();
      }

      // Close the current tab
      removeTab(tabId);

      // Check if there's an active CreateAgentScreen tab to return to
      const allTabs = getAllTabs();
      const createAgentTab = allTabs.find(tab => tab.path === "/create-agent");

      if (createAgentTab) {
        // If there's an active CreateAgentScreen tab, navigate to it
        setActiveTab(createAgentTab.id);
      } else {
        // Otherwise, activate the home tab - modal will be reopened automatically if needed
        // Note: We no longer need to navigate back to manage agents tab since it's now a modal
        if (currentTab?.state?.fromManageAgents || currentTab?.state?.fromManageAgentsModal) {
          // Just go to home - the modal can be reopened by the user if needed
          setActiveTab("home");
        } else {
          setActiveTab("home");
        }
      }

      // Clear persisted form data on successful creation/update
      if (!isEditMode) {
        dispatch(clearCreateSubagentFormData({ tabId }));
      }

      // Show success toast
      toast({
        title: isEditMode ? "Subagent Updated" : "Subagent Created",
        description: `Your subagent has been successfully ${isEditMode ? 'updated' : 'created'} and saved.`,
        duration: 5000,
      });
    } catch (error) {
      console.error(`Failed to ${isEditMode ? 'update' : 'create'} subagent:`, error);
      toast({
        title: "Error",
        description: `Failed to ${isEditMode ? 'update' : 'create'} subagent. Please try again.`,
        variant: "destructive",
        duration: 5000,
      });
    }
  };

  // Handler function for details modal
  const handleToolViewDetails = (tool: any) => {
    setDetailsModalData({
      type: 'tool',
      data: tool
    });
    setIsDetailsModalOpen(true);
  };

  // Use the shared details modal
  const DetailsModal = () => {
    if (!detailsModalData) return null;

    return (
      <DetailsModalComponent
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        type={detailsModalData.type}
        data={detailsModalData.data}
      />
    );
  };

  if(isMobile){
    return <section className="flex flex-col items-center justify-center min-h-screen p-6 text-center">
      <div className="max-w-md mx-auto">
        <h2 className="mb-4 text-2xl font-semibold">Desktop Required</h2>
        <p className="mb-6 text-gray-600">
          Creating subagents requires a desktop experience for the best workflow.
          Please open this page on a desktop or laptop computer.
        </p>
        <div className="text-sm text-gray-500">
           Switch to desktop to continue
        </div>
      </div>
    </section>;
  }

  return (
    <section
      style={{
        height: "calc(100vh - 56px)",
      }}
      className="flex w-full h-full bg-[#0A0A0B] text-white"
    >
       <img src={BackgroundSVG} alt="Background" className="absolute top-0 left-0 object-cover w-full h-full" />
      {/* First Section - Header with Step Indicator */}
      <div className="px-[120px] h-full max-w-[45%] z-[10]  flex items-center justify-center py-8">
        <div className="m-auto space-y-[32px] ">
          {/* Step Indicator */}
          <div className="space-y-4">
            <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className=""
          >
            <div className="text-[#DD99FF] flex gap-2 font-medium font-nothing">
              Step <span className="font-nothing">{currentStep} / 2</span>
            </div>
          </motion.div>

          {/* Main Title and Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className=""
          >
            <h1 className="mb-4 text-4xl font-semibold leading-tight text-white">
              {STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.title}
            </h1>
            <span className='text-[#737780] text-[14px] font-medium font-["Inter"]'>
              {
                STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                  ?.description
              }
            </span>
          </motion.div>
          </div>

          <div className="h-[1px] bg-[#1F1F21] w-full"></div>

          {/* Welcome Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="rounded-xl"
          >
            <div className="flex items-start gap-4">
              <div className="flex-1">
                <h2 className="flex items-center gap-2 mb-3 text-xl font-semibold text-[#B7BECC]">
                  <img
                    src={
                      STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.icon
                    }
                    alt={
                      STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                        ?.iconAlt
                    }
                    className="inline-block w-6 h-6 mb-1"
                  />
                  {
                    STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                      ?.welcomeTitle
                  }
                </h2>
                  {currentStep === 1 && (
                  <InstructionList items={SUB_AGENT_INSTRUCTION_ITEMS} />
                )}
                {currentStep === 2 && (
                  <InstructionList items={TOOLS_INSTRUCTION_ITEMS} />
                )}

                <span className="text-[#ffffff]/80 italic text-[16px] font-medium font-['Inter']">
                  {STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]?.tips}
                </span>

                {/* Learn More Button */}
                <LearnMoreButton
                  text={
                    STEP_CONFIG[currentStep as keyof typeof STEP_CONFIG]
                      ?.learnMoreText || "Learn More"
                  }
                  className="mt-6"
                  onClick={() => setIsLearnMoreModalOpen(true)}
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Second Section - Form Fields */}
      <div className="w-[55%] p-6 h-full overflow-hidden relative">
        <div className="border w-full h-full bg-[#18181A] rounded-[8px] border-[#222224] relative flex flex-col">
          {/* Scrollable Content Area */}
          <div ref={scrollableAreaRef} className="scrollable-content-area flex-1 overflow-y-auto pt-[56px] pb-[105px]">
            {/* Dynamic Content Based on Step */}
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="space-y-[40px] px-[40px] pb-[40px]"
            >
            {currentStep === 1 ? (
              // Step 1: Basic Info and Agent Prompt
              <>
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-2">
                    <InfoSquareIcon size={24} color="#FFFFFF" />
                    <h2 className=" font-medium text-[#FFFFFF4D]">
                      Basic Info
                    </h2>
                  </div>

                  <div className="flex flex-col rounded-[16px] w-full gap-6 p-8 border border-[#222224] ">
                    <div className="flex flex-col w-full gap-3 ">
                      <div className="flex items-center justify-between">
                        <Label
                          htmlFor="subagent-name"
                          className="font-['Inter'] font-medium text-[#e4eeff]"
                        >
                          Subagent Name
                        </Label>
                      </div>
                      <Input
                        id="subagent-name"
                        value={isEditMode ? prettifyName(subagentName) : subagentName}
                        onChange={(e) => setSubagentName(e.target.value)}
                        placeholder="For eg : Integration agent, Support agent"
                        disabled={isEditMode}
                        className="!bg-[#FFFFFF05] font-['Inter'] h-[56px] max-h-[56px] !text-[16px] border-[#262629] text-white placeholder:text-white/20 focus:border-white/30 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 [&:-webkit-autofill]:!bg-[#FFFFFF05] [&:-webkit-autofill]:text-white [&:-webkit-autofill]:shadow-[inset_0_0_0px_1000px_rgba(255,255,255,0.02)] [&:-webkit-autofill:hover]:!bg-[#FFFFFF05] [&:-webkit-autofill:focus]:!bg-[#FFFFFF05] [&:-webkit-autofill:active]:!bg-[#FFFFFF05] disabled:opacity-60 disabled:cursor-not-allowed"
                      />
                    </div>
                    <div className="flex flex-col w-full gap-3">
                      <div className="flex items-center justify-between">
                        <Label
                          htmlFor="subagent-guidelines"
                          className="font-['Inter'] font-medium text-[#e4eeff]"
                        >
                          Sub-agent Definition
                        </Label>
                      </div>
                      <Textarea
                        id="subagent-guidelines"
                        value={subagentGuidelines}
                        onChange={(e) => setSubagentGuidelines(e.target.value)}
                        placeholder="Brief description of what this subagent does and when it should be called."
                        className="!bg-[#FFFFFF05] font-['Inter'] p-4 min-h-[120px] border-[#262629] text-white placeholder:text-white/20 focus:border-white/30 focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 resize-none"
                      />
                    </div>
                  </div>
                </div>


                {/* Agent Prompt Section */}
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-2">
                    <TextEditIcon size={24} color="#FFFFFF" />
                    <h2 className=" font-medium text-[#FFFFFF4D]">
                      Subagent Prompt
                    </h2>
                  </div>

                  {/* System Constraints */}
                  <div className="flex flex-col gap-12 border border-[#222224] p-10 px-8 rounded-[16px]">
                    {isLoadingPrompts ? (
                      <SystemConstraintsSkeleton count={3} />
                    ) : promptsError ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-red-400">
                          Error loading prompts. Using default sections.
                        </div>
                      </div>
                    ) : (
                      <>
                        {systemConstraintSections.map((section) => (
                          <SystemConstraintSection
                            key={section.id}
                            section={section}
                            value={systemConstraints}
                            onChange={setSystemConstraints}
                            dynamicVariableValues={dynamicVariableValues}
                            onDynamicVariableChange={handleDynamicVariableChange}
                          />
                        ))}
                      </>
                    )}
                        <SystemConstraintSection
                          key="system-prompts"
                          section={{
                            id: "system-prompts-readonly",
                            title: "System Constraints",
                            helpText: "Learn about system prompts",
                            readOnly: true,
                            showLock: true,
                            isDynamic: false,
                          }}
                          value={getSubagentPrompt()?.prompt || ""}
                          onChange={() => {}} // No-op since it's read-only
                        />
                  </div>
                </div>
              </>
            ) : currentStep === 2 ? (
              // Step 2: Tools Selection
              <>
                {/* Warning Banner */}
                <div className="flex items-center gap-3 p-4 bg-[#FFAE6614] rounded-[8px]">
                  <InfoSquareIcon size={24} color="#FFAE66" fillOpacity={0.7} />
                  <p className="text-[#FFAE66B2] text-sm font-medium">
                    Select only the tools relevant to your use case. Adding too
                    many tools can reduce performance.
                  </p>
                </div>

                {/* Specialized Tools Section */}
                <div className="flex flex-col gap-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FourDashIcon size={24} color="#FFFFFF" />
                      <h2 className="font-medium text-[#FFFFFF4D]">
                        Specialized Tools
                      </h2>
                    </div>
                    {/* <button
                      type="button"
                      className="flex items-center gap-2 text-[#FFFFFF4D] transition-colors"
                    >
                      <span className="font-['Inter'] font-medium">
                        Request a New Tool
                      </span>
                      <img
                        src={ExternalSVG}
                        alt="External"
                        className="w-5 h-5"
                      />
                    </button> */}
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {isLoadingTools ? (
                      <ToolsListSkeleton count={5} />
                    ) : toolsError ? (
                      <div className="flex items-center justify-center py-8">
                        <div className="text-red-400">
                          Error loading tools. Using default tools.
                        </div>
                      </div>
                    ) : agentToolsData?.data &&
                      Array.isArray(agentToolsData.data) &&
                      agentToolsData.data.length > 0 && agentToolsData.data.filter((tool) => !tool.necessary_tool).length > 0 ? (
                      // Show API tools - only non-necessary tools in this section
                      agentToolsData.data
                        .filter((tool) => !tool.necessary_tool)
                        .map((tool) => (
                          <ToolItem
                            key={tool.name}
                            tool={tool}
                            isSelected={selectedTools.includes(tool.name)}
                            onCheckedChange={(checked) => handleToolCheckedChange(tool.name, checked)}
                            variant="subagent"
                            isNecessary={false}
                            onViewDetails={handleToolViewDetails}
                          />
                        ))
                    ) : <div>
                      <p className="text-[#8A8F98] text-sm text-center p-8">
                        No tools available
                      </p>
                    </div>}
                  </div>
                </div>

                {/* Default Tools Section */}
                <div className="flex flex-col gap-3">
                  <div className="flex items-center gap-2">
                    <SettingIcon size={24} color="#FFFFFF" className="opacity-20" />
                    <h2 className=" font-medium text-[#FFFFFF4D]">
                      Default Tools
                    </h2>
                  </div>

                  <div className="border border-[#222224] rounded-[16px] overflow-clip p-4">
                    {isLoadingTools ? (
                      <ToolsListSkeleton count={2} />
                    ) : agentToolsData?.data &&
                    Array.isArray(agentToolsData.data) &&
                    agentToolsData.data.filter((tool) => tool.necessary_tool).length > 0 ? (
                      // Show necessary tools from API
                      agentToolsData.data
                        .filter((tool) => tool.necessary_tool)
                        .map((tool) => (
                          <ToolItem
                            key={tool.name}
                            tool={tool}
                            isSelected={true} // Always selected for necessary tools
                            onCheckedChange={undefined} // No toggle for necessary tools
                            variant="subagent"
                            isNecessary={true}
                            onViewDetails={handleToolViewDetails}
                          />
                        ))
                    ) : (
                      <div className="py-8 text-center">
                        <p className="text-[#8A8F98]">
                          No Default tools available
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </>
            ) : null}
            </motion.div>
          </div>

          {/* Fixed Footer */}
          <div className="absolute bottom-0 left-0 right-0 border-t-[1px] z-[99] rounded-b-[8px] py-7 px-6 h-[105px] bg-[#1A1A1C] border-[#262629]">
            <div className="flex items-center justify-between">
              {currentStep > 1 ? (
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(currentStep - 1);
                    // Reset scroll position when going back
                    if (scrollableAreaRef.current) {
                      scrollableAreaRef.current.scrollTop = 0;
                    }
                  }}
                  className="flex items-center gap-2 px-6 py-3 font-semibold text-[#939399] bg-[#222224] hover:bg-[#FFFFFF10] rounded-full transition-colors"
                >
                  <img src={BackGray} alt="Previous" className="w-5 h-5" />
                  Previous Step
                </button>
              ) : (
                <div></div>
              )}

              <button
                type="button"
                onClick={handleContinue}
                disabled={isCreatingPrompt || isUpdatingPrompt || !canProceed()}
                className={cn("flex items-center gap-2 px-6 py-3 font-semibold text-black bg-white rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed",
                  currentStep === 2 && "bg-[#DD99FF] hover:bg-[#DD99FF90]"
                )}
              >
                {(isCreatingPrompt || isUpdatingPrompt) && currentStep === 2 ? (
                  <>
                    {isEditMode ? 'Updating' : 'Creating'} Subagent...
                    <div className="w-4 h-4 border-2 border-black rounded-full border-t-transparent animate-spin"></div>
                  </>
                ) : (
                  <>
                    {isEditMode
                      ? EDIT_BUTTON_TEXTS[currentStep as keyof typeof EDIT_BUTTON_TEXTS]
                      : BUTTON_TEXTS[currentStep as keyof typeof BUTTON_TEXTS]
                    }
                    <img src={NextArrow} alt="Next" className="w-5 h-5" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Details Modal */}
      <DetailsModal />

      {/* Learn More Modal */}
      <LearnMoreModal
        isOpen={isLearnMoreModalOpen}
        onOpenChange={setIsLearnMoreModalOpen}
        currentStep={currentStep}
        isSubagent={true}
      />
    </section>
  );
};
