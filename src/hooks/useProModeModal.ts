import { useEffect, useState } from 'react';
import { useProModePayment } from './useProModePayment';
import { hasSeenProModalAutoOpen, markProModalAutoOpenAsSeen } from '@/lib/utils/modalStateManager';

export interface UseProModeModalReturn {
  isModalOpen: boolean;
  setModalOpen: (open: boolean) => void;
  shouldShowThankYou: boolean;
  isAlreadyBooked: boolean;
  openModalForBookedUser: () => void;
  isBookedUserFlow: boolean;
}

export const useProModeModal = (): UseProModeModalReturn => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [shouldShowThankYou, setShouldShowThankYou] = useState(false);
  const [isBookedUserFlow, setIsBookedUserFlow] = useState(false);

  const { isProModeUser, paidBefore30July, checkPaymentStatus } = useProModePayment();

  // Check for showThankYou flag in localStorage on mount
  useEffect(() => {
    const showThankYou = localStorage.getItem('showThankYou');

    if (showThankYou === 'pro_mode') {
      setShouldShowThankYou(true);
      setIsModalOpen(true);

      // Check payment status with delay
      setTimeout(() => {
        checkPaymentStatus();
      }, 3000);
    }
  }, [checkPaymentStatus]);

  // Auto-open modal for users who paid before July 30th (one time only)
  useEffect(() => {
    if (isProModeUser && paidBefore30July && !hasSeenProModalAutoOpen()) {
      setIsModalOpen(true);
    }
  }, [isProModeUser, paidBefore30July]);

  // Update shouldShowThankYou based on pro mode status
  // useEffect(() => {
  //   if (isProModeUser) {
  //     // setShouldShowThankYou(true);
  //   }
  // }, [isProModeUser]);

  const setModalOpen = (open: boolean) => {
    setIsModalOpen(open);
    if (!open) {
      setShouldShowThankYou(false);
      setIsBookedUserFlow(false);

      // Mark auto-open as seen when modal is closed (for users who paid before July 30th)
      if (isProModeUser && paidBefore30July && !hasSeenProModalAutoOpen()) {
        markProModalAutoOpenAsSeen();
      }

      // Clear the localStorage flag when modal is closed
      const showThankYou = localStorage.getItem('showThankYou');
      if (showThankYou === 'pro_mode') {
        console.log('useProModeModal: Clearing showThankYou flag from localStorage');
        localStorage.removeItem('showThankYou');
        localStorage.removeItem('utm_term');
      }
    }
  };

  const openModalForBookedUser = () => {
    console.log('useProModeModal: Opening modal for already booked user');
    setIsBookedUserFlow(true);
    setShouldShowThankYou(false); // Ensure we don't show thank you step immediately
    setIsModalOpen(true);
  };

  return {
    isModalOpen,
    setModalOpen,
    shouldShowThankYou,
    isAlreadyBooked: isProModeUser,
    openModalForBookedUser,
    isBookedUserFlow,
  };
};
