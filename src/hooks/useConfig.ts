import { useGetConfigQuery } from '@/store/api/apiSlice';
import { useAuth } from '@/contexts';
import { useEffect, useState } from 'react';
import { useIsEmergentUser } from '@/hooks/useIsEmergentUser';
import { GlobalConfig } from '@/types/config';

// i have replaced this with new config query from apiSlice , now it has reload query inbuilt
export const useConfig = () => {

  const [universalKeyEnabled, setUniversalKeyEnabled] = useState(false);
  const { user } = useAuth();
  const isEmergentUser = useIsEmergentUser();
  const {
    data: config,
    isLoading: loading,
    error,
    refetch,
  } = useGetConfigQuery(undefined, {
    skip: !user,
    pollingInterval: 5 * 60 * 1000,
  });

  const checkUniversalKeyEnabled = () => {
    if (config) {
      setUniversalKeyEnabled(isUniversalKeyEnabled(config, user || undefined, isEmergentUser));
    }
  };

  useEffect(() => {
    checkUniversalKeyEnabled();
  }, [config, user, isEmergentUser]);

  return {
    config,
    loading,
    error: error ? new Error('Failed to fetch config') : null,
    refetch,
    universalKeyEnabled,
  };
};


export const isUniversalKeyEnabled = (config: GlobalConfig, user?: { id: string }, isEmergentUser?: boolean) => {
  if (isEmergentUser) {
    return true;
  }

  if(config?.feature_flags?.universal_key === true){
    return true;
  }

  if (Array.isArray(config?.feature_flags?.universal_key) && user?.id) {
    const lastLetter = user.id.slice(-1).toLowerCase();
    return config.feature_flags.universal_key.includes(lastLetter);
  }

  return false;
};