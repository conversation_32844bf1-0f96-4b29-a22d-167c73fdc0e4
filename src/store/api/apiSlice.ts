import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase';
import { config } from '@/config';
import { Job } from '@/types/job';
import { GlobalConfig } from '@/types/config';
import { JobResponse } from '@/services/agentApi';
import { fetchConfigVersion } from '@/services/postHogService';
import { getAppVersion } from '@/lib/utils/getAppVersion';

// Types for file upload
export interface DirectUploadRequest {
  jobId: string;
  file: File;
  fileName: string;
  description?: string;
}

export interface DirectUploadResponse {
  success: boolean;
  message: string;
  path: string;
  artifact_created: boolean;
  artifact_id?: string;
  artifact_message?: string;
}

export interface SubmitHITLRequest {
  payload: any;
  client_ref_id: string;
  resume?: boolean;
  finalize_artifact_upload?: {
    artifacts: Array<{
      artifact_id: string;
      entity_id: string;
      entity_type: "job";
      visibility: "public" | "private";
      file_name: string;
      description?: string;
      file_path?: string;
    }>;
  };
}

// Types for GitHub API responses
export interface GitHubInstallation {
  installation_id: string;
  account_login: string;
  account_type: string;
  user_github_login: string;
  app_slug: string;
  isPrimary?: boolean;
  account: {
    login: string;
    id: number;
    type: string;
  };
  target_type: string;
}
// Types for Credits API responses
export interface CreditsBalance {
  error?: string;
  ecu_balance: number;
  monthly_credits_balance: number;
  top_up_credit_balance: number;
  monthly_credits_refresh_date: string;
  subscription: {
    id: string;
    name: string;
    status: "active" | "past_due" | "unpaid" | "initiated_cancellation" | 'free';
    expires_at: boolean;
    monthly_credit_limit: number;
    subscription_details: {
      id: string,
      amount: number
    }
  };
}

// Pro Mode Payment Types
export interface ProModePayment {
  id: string;
  session_id: string;
  email: string;
  client_reference_id: string;
  tag: string;
  amount: number;
  currency: string;
  payment_status: string;
  created_at: string;
}

export interface ProModePaymentResponse {
  exists: boolean;
  payment?: ProModePayment;
}

// Subscription Plans Types
export interface SubscriptionPlan {
  id: string;
  tier_name: string;
  amount: number;
  ecu_credits: number;
  billing_period: "MONTHLY" | "DAILY";
  currency: string;
}

export interface CurrentSubscription {
  id: string;
  tier_name: string;
  amount: number;
  ecu_credits: number;
  billing_period: "MONTHLY" | "DAILY";
  status: "active" | "past_due" | "unpaid" | "initiated_cancellation";
  is_scheduled_for_change: boolean;
  scheduled_plan: SubscriptionPlan | null;
}

export interface SubscriptionPlansResponse {
  current_subscription: CurrentSubscription | null;
  available_plans: SubscriptionPlan[];
}

// Subscription Status Types
export interface SubscriptionStatusResponse {
  status: "success" | "pending" | "failed" | "cancelled";
  message: string;
  plan_name?: string;
  error?: string;
}

export interface GitHubRepository {
  id: string;
  repository_id: string;
  name: string;
  full_name: string;
  private: boolean;
  visibility: boolean;
  installation_id: string;
  permissions: {
    admin: boolean;
    push: boolean;
    pull: boolean;
    maintain: boolean;
    triage: boolean;
  };
}

export interface GitHubBranch {
  name: string;
  commit: {
    sha: string;
    url: string;
  };
  protected: boolean;
}

export interface GitHubUserDetails {
  id: string;
  github: {
    authorized: boolean;
    account_name: string;
  };
  github_installations: any[];
  referral_info: {
    referral_code: string;
    referrer_email: string | null;
    signed_up_at: string;
  };
  referral_message?: string;
}



export interface CreateRepositoryRequest {
  installation_id: string;
  org?: string;
  name: string;
}

export interface PushToGitHubRequest {
  account_login: string;
  repo_name: string;
  branch_name: string;
  is_new_repo?: boolean;
  force?: boolean;
}

export interface PreviewURLResponse {
  preview_url: string;
  vscode_url: string;
  password: string;
  base_preview_url: string;
}

// Budget API Types
export interface BudgetInfo {
  max_budget: number;
  current_usage: number;
}

export interface UpdateBudgetRequest {
  amount: number;
}

// Base query with authentication
const baseQuery = fetchBaseQuery({
  baseUrl: config.apiBaseUrl,
  prepareHeaders: async (headers) => {
    const session = await supabase.auth.getSession();
    if (session.data.session?.access_token) {
      headers.set('Authorization', `Bearer ${session.data.session.access_token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

// File server base query for file uploads
const fileServerBaseQuery = fetchBaseQuery({
  baseUrl: config._file_upload_url,
  prepareHeaders: async (headers) => {
    const session = await supabase.auth.getSession();
    if (session.data.session?.access_token) {
      headers.set('Authorization', `Bearer ${session.data.session.access_token}`);
    }

    // Conditionally set eph_name header based on API base URL
    const apiBaseUrl = config.apiBaseUrl;
    if (apiBaseUrl && apiBaseUrl.includes('.run.app')) {
      // Extract service name from URL pattern: https://agent-service-{serviceName}-{number}.us-central1.run.app
      const match = apiBaseUrl.match(/agent-service-([^-]+)-\d+\..*\.run\.app/);
      if (match && match[1]) {
        headers.set('eph_name', match[1]);
      }
    }

    // Don't set Content-Type for FormData - let browser handle it
    return headers;
  },
});

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery,
  tagTypes: ['GitHubInstallations', 'GitHubRepositories', 'GitHubBranches', 'GitHubUserDetails', 'PreviewURL', 'Referral', 'DownloadCode', 'Credits', 'ProModePayment', 'Budget', 'Jobs', 'Config', 'SubscriptionPlans', 'SubscriptionStatus'],
  endpoints: (builder) => ({
    // GitHub User Details
    getGitHubUserDetails: builder.query<GitHubUserDetails, void>({
      query: () => {
        // Check localStorage for referral code
        let referralCode: string | null = null;
        try {
          const pendingReferralData = localStorage.getItem('pending_referral_code');
          if (pendingReferralData) {
            const parsedData = JSON.parse(pendingReferralData);
            referralCode = parsedData.code;
          }
        } catch (error) {
          console.error('Failed to parse referral code from localStorage:', error);
        }

        // Build query with referral parameter if exists
        const baseUrl = '/user/details';
        if (referralCode) {
          const url= `${baseUrl}?referral_code=${encodeURIComponent(referralCode)}`;
          console.log('Fetching user details with referral code:', url);

          return url;
        }
        return baseUrl;
      },
      providesTags: ['GitHubUserDetails'],
      transformResponse: (response: any) => {
        // Handle referral message if present
        if (response.referral_message) {
          console.log('Referral registration successful:', response.referral_message);

          // Store referral modal data in localStorage
          try {
            const modalData = {
              shouldShow: true,
              message: response.referral_message,
              reward: 5, // Default reward amount
              timestamp: Date.now(),
            };
            localStorage.setItem('referral_modal_data', JSON.stringify(modalData));
            console.log('Stored referral modal data in localStorage:', modalData);
          } catch (error) {
            console.error('Failed to store referral modal data:', error);
          }
        }
        return response;
      },
      transformErrorResponse: (response: any) => ({
        id: null,
        github: { authorized: false, account_name: '' },
        referral_info: {
          referral_code: '',
          referrer_email: null,
          signed_up_at: ''
        },
        github_installations: [],
        error: response.data?.detail || 'Failed to fetch user details'
      }),
    }),

    // GitHub Installations
    getGitHubInstallations: builder.query<GitHubInstallation[], void>({
      query: () => '/github/installations',
      providesTags: ['GitHubInstallations'],
      transformResponse: (response: any) => {
        if (response.error) {
          throw new Error(response.error);
        }

        // The API returns an array of installations directly
        const installations = Array.isArray(response) ? response : [];

        // Transform the data to match our expected structure
        // Note: isPrimary will be set by the component using userDetails
        return installations.map((installation: any) => ({
          installation_id: installation.account?.login || installation.account_login,
          account_login: installation.account?.login || installation.account_login,
          account_type: installation.target_type || installation.account_type,
          user_github_login: installation.account?.login || installation.user_github_login,
          app_slug: installation.app_slug,
          isPrimary: false, // Will be updated by component
          account: installation.account,
          target_type: installation.target_type,
        }));
      },
      transformErrorResponse: (response: any) => ({
        installations: [],
        error: response.data?.message || 'Failed to fetch installations'
      }),
    }),

    // GitHub Repositories
    getGitHubRepositories: builder.query<GitHubRepository[], string | void>({
      query: (accountLogin) => {
        const endpoint = accountLogin
          ? `/github/repositories?account_login=${encodeURIComponent(accountLogin)}`
          : '/github/repositories';
        return endpoint;
      },
      providesTags: (_result, _error, accountLogin) => [
        { type: 'GitHubRepositories', id: accountLogin || 'ALL' }
      ],
      transformResponse: (response: any, _meta, accountLogin) => {
        if (response.error) {
          throw new Error(response.error);
        }

        // The API returns { repositories: [...] }
        const repositories = response.repositories || [];

        // If we have an accountLogin filter, filter the repositories
        if (accountLogin) {
          return repositories.filter((repo: any) =>
            repo.owner?.login === accountLogin ||
            repo.full_name?.startsWith(`${accountLogin}/`)
          );
        }

        return repositories;
      },
      transformErrorResponse: (response: any) => ({
        repositories: [],
        error: response.data?.message || 'Failed to fetch repositories'
      }),
    }),

    // GitHub Branches
    getGitHubBranches: builder.query<GitHubBranch[], { accountLogin: string; repoName: string }>({
      query: ({ accountLogin, repoName }) => {
        const encodedAccountLogin = encodeURIComponent(accountLogin);
        const encodedRepoName = encodeURIComponent(repoName);
        return `/github/branches/${encodedAccountLogin}/${encodedRepoName}`;
      },
      providesTags: (_result, _error, { accountLogin, repoName }) => [
        { type: 'GitHubBranches', id: `${accountLogin}/${repoName}` }
      ],
      transformResponse: (response: any) => {
        if (response.error) {
          throw new Error(response.error);
        }
        return Array.isArray(response) ? response : 
               Array.isArray(response.branches) ? response.branches : [];
      },
      transformErrorResponse: (response: any) => ({
        branches: [],
        error: response.data?.message || 'Failed to fetch branches'
      }),
    }),

    // Create GitHub Repository
    createGitHubRepository: builder.mutation<any, CreateRepositoryRequest>({
      query: (data) => ({
        url: '/github/repositories',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['GitHubRepositories'],
    }),

    // Push to GitHub
    pushToGitHub: builder.mutation<any, { jobId: string; data: PushToGitHubRequest }>({
      query: ({ jobId, data }) => ({
        url: `/jobs/v0/push_to_github/${jobId}`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['GitHubRepositories', 'GitHubBranches'],
    }),

    // Save GitHub Installation
    saveGitHubInstallation: builder.mutation<any, { installationId: string; code: string }>({
      query: ({ installationId, code }) => ({
        url: '/github/installation',
        method: 'POST',
        body: {
          installation_id: installationId,
          code,
        },
      }),
      invalidatesTags: ['GitHubInstallations', 'GitHubUserDetails'],
    }),

    // Get Job Preview URL
    getJobPreviewUrl: builder.query<PreviewURLResponse, string>({
      query: (jobId) => `/jobs/v0/${jobId}/preview`,
      providesTags: (_result, _error, jobId) => [
        { type: 'PreviewURL', id: jobId }
      ],
      transformResponse: (response: PreviewURLResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to fetch preview URL'
      }),
    }),


    // Get Code Download URL
    getCodeDownloadUrl: builder.query<{
      job_id: string;
      pod_id: string;
      file_name: string;
      download_url: string;
      expires_in_minutes: number;
    }, string>({
      query: (jobId) => `/download/pod-backup/download-zip?job_id=${jobId}`,
      providesTags: (_result, _error, jobId) => [
        { type: 'DownloadCode', id: jobId }
      ],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || 'Failed to fetch download URL'
      }),
    }),

    // Get Credits Balance with retry logic
    getCreditsBalance: builder.query<CreditsBalance, void>({
      query: () => '/credits/balance',
      providesTags: ['Credits'],
      transformResponse: (response: CreditsBalance) => {
        // If the response has an error field, throw it to trigger error handling
        if (response.error) {
          throw new Error(response.error);
        }
        return response;
      },
      transformErrorResponse: (response: any) => {
        console.error("Credits API error:", response);
        return {
          error: response.data?.message || response.data?.detail || 'Failed to fetch credits balance',
          ecu_balance: 0,
          top_up_credit_balance: 0,
          monthly_credits_balance: 0,
          monthly_credits_refresh_date: "",
          subscription: {
            id: "",
            name: "",
            status: "free" as const,
            expires_at: false,
            monthly_credit_limit: 5,
            subscription_details: {
              id: "",
              amount: 0
            }
          }
        };
      },
    }),

    // Check Pro Mode Payment Status
    checkProModePayment: builder.query<ProModePaymentResponse, { tag: string; }>({
      query: ({ tag }) => `/payments/custom-payments/check?tag=${encodeURIComponent(tag)}`,
      providesTags: ['ProModePayment'],
      transformResponse: (response: ProModePaymentResponse) => response,
      transformErrorResponse: (response: any) => ({
        exists: false,
        error: response.data?.message || response.data?.detail || 'Failed to check payment status'
      }),
    }),

    // Get Subscription Plans
    getSubscriptionPlans: builder.query<SubscriptionPlansResponse, void>({
      query: () => '/payments/subscription/plans',
      providesTags: ['SubscriptionPlans'],
      transformResponse: (response: SubscriptionPlansResponse) => response,
      transformErrorResponse: (response: any) => ({
        current_subscription: null,
        available_plans: [],
        error: response.data?.message || response.data?.detail || 'Failed to fetch subscription plans'
      }),
    }),

    // Check Subscription Status
    checkSubscriptionStatus: builder.query<SubscriptionStatusResponse, string>({
      query: (paymentId) => `/payments/${paymentId}/status`,
      providesTags: (_result, _error, paymentId) => [
        { type: 'SubscriptionStatus', id: paymentId }
      ],
      transformResponse: (response: SubscriptionStatusResponse) => response,
      transformErrorResponse: (response: any) => ({
        status: "failed" as const,
        message: response.data?.message || response.data?.detail || 'Failed to check subscription status',
        error: response.data?.message || response.data?.detail || 'Failed to check subscription status'
      }),
    }),

    // Budget API endpoints
    getBudget: builder.query<BudgetInfo, string>({
      query: (jobId) => `/budget/${jobId}`,
      providesTags: (_result, _error, jobId) => [
        { type: 'Budget', id: jobId }
      ],
      transformResponse: (response: any) => {
        if (!response.success) {
          throw new Error(response.data?.error || 'Failed to fetch budget');
        }
        return response.data;
      },
      transformErrorResponse: (response: any) => ({
        error: response.data?.error || response.data?.detail || 'Failed to fetch budget information'
      }),
    }),

    updateBudget: builder.mutation<BudgetInfo, { jobId: string; amount: number }>({
      query: ({ jobId, amount }) => ({
        url: `/budget/${jobId}`,
        method: 'PUT',
        body: { amount },
      }),
      invalidatesTags: (_result, _error, { jobId }) => [
        { type: 'Budget', id: jobId }
      ],
      transformResponse: (response: any) => {
        if (!response.success) {
          throw new Error(response.data?.error || 'Failed to update budget');
        }
        return response.data;
      },
      transformErrorResponse: (response: any) => ({
        error: response.data?.error || response.data?.detail || 'Failed to update budget'
      }),
    }),

    // Jobs API endpoints
    getJobs: builder.query<Job[], void>({
      query: () => '/jobs/?limit=50',
      providesTags: ['Jobs'],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch jobs'
      }),
    }),

    // Get individual job by ID
    getJob: builder.query<JobResponse, string>({
      query: (jobId) => `/jobs/v0/${jobId}/`,
      providesTags: (_result, _error, jobId) => [
        { type: 'Jobs', id: jobId }
      ],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch job'
      }),
    }),

    // Config API endpoints
    getConfig: builder.query<GlobalConfig, void>({
      queryFn: async (_arg, _api, _extraOptions, baseQuery) => {
        try {
          const payload = await fetchConfigVersion();
          const version = payload || (await getAppVersion());

          const result = await baseQuery({
            url: '/repositories/config',
            params: { app_version: version }
          });

          if (result.error) {
            return { error: result.error };
          }

          return { data: result.data as GlobalConfig };
        } catch (error) {
          return {
            error: {
              status: 'CUSTOM_ERROR',
              error: error instanceof Error ? error.message : 'Failed to fetch config'
            } as any
          };
        }
      },
      providesTags: ['Config'],
    }),

    uploadFileStream: builder.mutation<DirectUploadResponse, DirectUploadRequest>({
      queryFn: async ({ jobId, file, fileName, description }, api) => {
        // Build query parameters
        const params = new URLSearchParams({
          job_id: jobId,
          file_name: fileName,
        });
        if (description) {
          params.set('description', description);
        }

        const result = await fileServerBaseQuery({
          url: `/api/v1/files/stream?${params}`,
          method: 'POST',
          body: file,
          headers: {
            'Content-Type': 'application/octet-stream',
            'Content-Length': file.size.toString(),
          },
        }, api, {});

        if (result.error) {
          return { error: result.error };
        }

        return { data: result.data as DirectUploadResponse };
      },
    }),

    // Submit HITL with optional artifact finalization
    submitHITL: builder.mutation<any, SubmitHITLRequest>({
      query: ({ payload, client_ref_id, resume = false, finalize_artifact_upload }) => {
        const data: any = {
          client_ref_id: client_ref_id,
          payload: {
            processor_type: "env_only",
            is_cloud: true,
            env_image: payload.env_image,
            branch: "",
            repository: "",
            ...payload,
          },
          model_name: payload.model_name,
          resume: resume,
        };

        // Add finalize_artifact_upload if provided
        if (finalize_artifact_upload) {
          data.finalize_artifact_upload = finalize_artifact_upload;
        }

        if (resume) {
          data.id = client_ref_id;
        }

        return {
          url: '/jobs/v0/hitl-queue/',
          method: 'POST',
          body: data,
        };
      },
    }),

    // Deploy Expo App (Publish)
    deployExpoApp: builder.mutation<any, string>({
      query: (jobId) => ({
        url: `/jobs/v0/${jobId}/persist-environment`,
        method: 'POST',
        body: {},
      }),
      invalidatesTags: (_result, _error, jobId) => [
        { type: 'Jobs', id: jobId }
      ],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.detail || response.data?.error || 'Failed to publish app'
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetGitHubUserDetailsQuery,
  useGetGitHubInstallationsQuery,
  useGetGitHubRepositoriesQuery,
  useGetGitHubBranchesQuery,
  useCreateGitHubRepositoryMutation,
  usePushToGitHubMutation,
  useSaveGitHubInstallationMutation,
  useGetJobPreviewUrlQuery,
  useLazyGetJobPreviewUrlQuery,
  useGetCodeDownloadUrlQuery,
  useLazyGetCodeDownloadUrlQuery,
  useGetCreditsBalanceQuery,
  useLazyGetCreditsBalanceQuery,
  useCheckProModePaymentQuery,
  useLazyCheckProModePaymentQuery,
  useGetSubscriptionPlansQuery,
  useLazyGetSubscriptionPlansQuery,
  useCheckSubscriptionStatusQuery,
  useLazyCheckSubscriptionStatusQuery,
  useGetBudgetQuery,
  useLazyGetBudgetQuery,
  useUpdateBudgetMutation,
  useGetJobsQuery,
  useLazyGetJobsQuery,
  useGetJobQuery,
  useLazyGetJobQuery,
  useGetConfigQuery,
  useLazyGetConfigQuery,
  useSubmitHITLMutation,
  useUploadFileStreamMutation,
  useDeployExpoAppMutation,
} = apiSlice;
