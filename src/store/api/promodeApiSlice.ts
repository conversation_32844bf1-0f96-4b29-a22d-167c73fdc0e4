import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { supabase } from '@/lib/supabase';
import { config } from '@/config';

// Types for ProMode API responses
export interface Prompt {
  id: string;
  prompt_name: string;
  model_name: string;
  dynamic_variables: string[];
  default_variable_values: Record<string, string>;
  created_at: string;
  updated_at: string;
  prompt: string;
  skill_description: string;
}

export interface ProModePromptsResponse {
  prompts: Prompt[];
  count: number;
  limit: number;
  offset: number;
}

// Types for creating user prompts
export interface CreateUserPromptRequest {
  prompt_name: string;
  description?: string;
  skill_description?: string;
  parent_prompt: string;
  base_prompt_id: string;
  prompt_subagents: string[];
  prompt_tools: string[] | null;
  prompt_variables: Record<string, string>;
  system_prompt_injection_templates: string[];
  is_main_agent: string;
}

// Types for updating user prompts
export interface UpdateUserPromptRequest {
  prompt_name?: string;
  description?: string;
  skill_description?: string;
  parent_prompt?: string;
  base_prompt_id?: string;
  prompt_subagents?: string[];
  prompt_tools?: string[] | null;
  prompt_variables?: Record<string, string>;
  system_prompt_injection_templates?: string[];
  is_main_agent?: string;
}

export interface UpdateUserPromptResponse {
  id: string;
  prompt_name: string;
  parent_prompt: string;
  base_prompt_id: string;
  prompt_subagents: string[];
  prompt_tools: string[] | null;
  prompt_variables: Record<string, string>;
  system_prompt_injection_templates: string[];
  is_main_agent: string;
  created_at: string;
  updated_at: string;
}

export interface CreateUserPromptResponse {
  id: string;
  prompt_name: string;
  parent_prompt: string;
  base_prompt_id: string;
  prompt_subagents: string[];
  prompt_tools: string[] | null;
  prompt_variables: Record<string, string>;
  system_prompt_injection_templates: string[];
  is_main_agent: string;
  created_at: string;
  updated_at: string;
}

// Types for agent tools
export interface AgentTool {
  name: string;
  status: boolean;
  necessary_tool: boolean;
  user_descriptions: {
    short_desc: string;
    description: string;
  };
}

export interface AgentToolsResponse {
  data: AgentTool[];
}

// Types for user prompts
export interface UserPrompt {
  id: string;
  user_id: string;
  base_prompt_id: string;
  prompt_name: string;
  description?: string;
  parent_prompt: string;
  prompt_variables: Record<string, any>;
  prompt_subagents: string[];
  prompt_tools: string[];
  system_prompt_injection_templates: string[];
  is_main_agent: boolean;
  created_at: string;
  updated_at: string;
  skill_description: string;
}

export interface UserPromptsResponse {
  user_prompts: UserPrompt[];
  count: number;
  limit: number;
  offset: number;
}

export interface GetUserPromptsParams {
  limit?: number;
  offset?: number;
  is_main_agent?: boolean;
}

// Base query with authentication
const promodeBaseQuery = fetchBaseQuery({
  baseUrl: config.apiBaseUrl,
  prepareHeaders: async (headers) => {
    const session = await supabase.auth.getSession();
    if (session.data.session?.access_token) {
      headers.set('Authorization', `Bearer ${session.data.session.access_token}`);
    }
    headers.set('Content-Type', 'application/json');
    return headers;
  },
});

export const promodeApiSlice = createApi({
  reducerPath: 'promodeApi',
  baseQuery: promodeBaseQuery,
  tagTypes: ['ProModePrompts', 'AgentTools'],
  endpoints: (builder) => ({
    // Fetch base prompts
    getBasePrompts: builder.query<ProModePromptsResponse, void>({
      query: () => '/prompts/base?include_prompt=true',
      providesTags: ['ProModePrompts'],
      transformResponse: (response: ProModePromptsResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch base prompts'
      }),
    }),

    // Get agent tools
    getAgentTools: builder.query<AgentToolsResponse, string>({
      query: (agentName) => `/prompts/agents/${encodeURIComponent(agentName)}/tools`,
      providesTags: ['AgentTools'],
      transformResponse: (response: AgentToolsResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch agent tools'
      }),
    }),

    // Get user prompts
    getUserPrompts: builder.query<UserPromptsResponse, GetUserPromptsParams | void>({
      query: (params) => {
        const { limit = 50, offset = 0, is_main_agent } = params || {};
        let queryString = `/prompts/user?limit=${limit}&offset=${offset}`;
        if (is_main_agent !== undefined) {
          queryString += `&is_main_agent=${is_main_agent}`;
        }
        return queryString;
      },
      providesTags: ['ProModePrompts'],
      transformResponse: (response: UserPromptsResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch user prompts'
      }),
    }),

    // Create user prompt
    createUserPrompt: builder.mutation<CreateUserPromptResponse, CreateUserPromptRequest>({
      query: (data) => ({
        url: '/prompts/user',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['ProModePrompts'],
      transformResponse: (response: CreateUserPromptResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to create user prompt'
      }),
    }),

    // Update user prompt
    updateUserPrompt: builder.mutation<UpdateUserPromptResponse, { id: string; data: UpdateUserPromptRequest }>({
      query: ({ id, data }) => ({
        url: `/prompts/user/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['ProModePrompts'],
      transformResponse: (response: UpdateUserPromptResponse) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to update user prompt'
      }),
    }),

    // Get single user prompt by ID
    getUserPrompt: builder.query<UserPrompt, string>({
      query: (id) => `/prompts/user/${id}`,
      providesTags: (_result, _error, id) => [
        { type: 'ProModePrompts', id }
      ],
      transformResponse: (response: UserPrompt) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to fetch user prompt'
      }),
    }),

    // Delete user prompt
    deleteUserPrompt: builder.mutation<{ success: boolean; message: string }, string>({
      query: (id) => ({
        url: `/prompts/user/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['ProModePrompts'],
      transformResponse: (response: any) => response,
      transformErrorResponse: (response: any) => ({
        error: response.data?.message || response.data?.detail || 'Failed to delete user prompt'
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetBasePromptsQuery,
  useLazyGetBasePromptsQuery,
  useGetAgentToolsQuery,
  useLazyGetAgentToolsQuery,
  useGetUserPromptsQuery,
  useLazyGetUserPromptsQuery,
  useGetUserPromptQuery,
  useLazyGetUserPromptQuery,
  useCreateUserPromptMutation,
  useUpdateUserPromptMutation,
  useDeleteUserPromptMutation,
} = promodeApiSlice;
