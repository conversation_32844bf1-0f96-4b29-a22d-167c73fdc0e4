import { useCredits } from "@/contexts";
import CopperCoin from "@/assets/copper-coin.svg";
import { UpgradeTierButton } from "./UpgradeTierButton";
import SparklingStar from "@/assets/SparklingStar.svg";
import { BuyCreditsButton } from "./BuyCreditsButton";
import { ManageSubscriptionButton } from "./ManageSubscriptionButton";
import InfoSVG from "@/assets/payments/info.svg";
import QuestionSVG from "@/assets/payments/question.svg";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ExternalSVG from "@/assets/payments/external.svg";
import { cn } from "@/lib/utils";
import { formatDateOnly } from "@/lib/utils/dateFormatter";
import useIsMobileApp from "@/hooks/useIsMobileApp";

interface TierBadgeProps {
  tier: string;
}

function TierBadge({ tier }: TierBadgeProps) {
  const baseClasses = "px-4 py-2 rounded-full max-h-[36px] flex items-center justify-center font-['Inter'] font-semibold";

  if (tier === "free") {
    return (
      <div className={`${baseClasses} bg-[#363637]`}>
        Free Tier
      </div>
    );
  }

  // All paid tiers (starter, standard, pro, etc.) have the same styling
  const paidTierClasses = `${baseClasses} transition-all ease-in-out duration-200 border border-[#37342A] bg-[#37342A] text-[#F3CA5F]`;

  if (tier === "starter") {
    return (
      <div className={paidTierClasses}>
        Starter Tier
        <img src={SparklingStar} alt="tier icon" className="w-5 h-5 ml-2" />
      </div>
    );
  }

  if (tier === "standard") {
    return (
      <div className={paidTierClasses}>
        Standard
        <img src={SparklingStar} alt="tier icon" className="w-5 h-5 ml-2" />
      </div>
    );
  }

  if (tier === "pro_mode") {
    return (
      <div className={paidTierClasses}>
        Pro Mode
        <img src={SparklingStar} alt="tier icon" className="w-5 h-5 ml-2" />
      </div>
    );
  }

  // Pro tier and other paid tiers (default case)
  return (
    <div className={paidTierClasses}>
      Pro Tier
      <img src={SparklingStar} alt="tier icon" className="w-5 h-5 ml-2" />
    </div>
  );
}

interface ActionButtonsProps {
  tier: string;
  isMobile: boolean;
}

function ActionButtons({ tier, isMobile }: ActionButtonsProps) {
  if (isMobile) {
    return (
      <div className="flex flex-col gap-3">
        <div className="w-full">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if ((window as any)?.ReactNativeWebView) {
                // Send message to React Native to open external browser
                (window as any).ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'OPEN_EXTERNAL_URL',
                  url: 'https://app.emergent.sh'
                }));
              }
            }}
            className="flex w-[90%] mt-2 ml-3 bg-[#F3CA5F] text-black hover:bg-[#E7A93C] p-2 rounded-lg justify-between font-semibold tracking-[-0.2px] items-center gap-2"
          >
            Manage Account
            <span className="text-lg">+</span>
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-3">
      {tier === "free" ? (
        <UpgradeTierButton />
      ) : (
        <>
          <ManageSubscriptionButton />
          <BuyCreditsButton />
        </>
      )}
    </div>
  );
}

interface FreeTierMessageProps {
  tier: string;
}

function FreeTierMessage({ tier }: FreeTierMessageProps) {
  if (tier !== "free") {
    return null;
  }

  return (
    <div className="mb-3">
      <p className="text-base font-medium text-white/50">
        You're on the Free tier with{" "}
        <span className="text-white">5 monthly credits</span>. Unlock
        additional features and credits by upgrading your plan.
      </p>
    </div>
  );
}

export function CreditsPopoverContent() {
  const { credits, tier, creditResponse } = useCredits();
  const isMobile = useIsMobileApp();


  return (
    <TooltipProvider>
      <div className="w-full p-[20px] px-4 bg-[#272829CC] backdrop-blur-xl rounded-xl text-white flex flex-col">
        <div className="flex items-start justify-between space-y-0">
          <div>
            <div className="flex items-center gap-1">
              <h2
                className="text-base text-white/50"
                style={{ fontFamily: "Inter", fontWeight: 500 }}
              >
                Available Credits
              </h2>
              <Tooltip delayDuration={0}>
                <TooltipTrigger asChild>
                  <img src={QuestionSVG} alt="Info" className="w-4 h-4 ml-1" />
                </TooltipTrigger>
                <TooltipContent
                  className="w-[364px] p-4 pl-2 mt-[6px] rounded-[12px] bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-2xl"
                  side="bottom"
                  align="end"
                  style={{ backdropFilter: "blur(20px)" }}
                >
                  <div className="space-y-4">
                    <div className="flex items-center justify-between pl-4">
                      <h3 className="font-['Inter'] font-semibold text-white">
                        About Credits
                      </h3>
                      <div className="flex items-center gap-1 text-xs cursor-pointer text-white/70 hover:text-white/90">
                        <span
                          className="font-['Inter'] text-[14px] text-[#FFFFFF99] underline"
                          onClick={() =>
                            window.open(
                              "https://atlas-kb.com/atlas-e74243keac/articles/769724-credits-and-pricing",
                              "_blank"
                            )
                          }
                        >
                          Learn more
                        </span>
                        <img
                          src={ExternalSVG}
                          alt="External"
                          className="w-5 h-5"
                        />
                      </div>
                    </div>
                    <div className="space-y-2 text-[13px] font-medium text-white/60">
                      <div className="flex items-start gap-2">
                        <div className="flex-shrink-0 w-1 h-1 mt-2 rounded-full bg-white/50"></div>
                        <p className="leading-[22px] font-['Inter']">
                          Emergent prioritizes credit usage by first drawing
                          from your monthly allocation, followed by any top-up
                          balance you've purchased.
                        </p>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="flex-shrink-0 w-1 h-1 mt-2 rounded-full bg-white/50"></div>
                        <p className="leading-[22px] font-['Inter']">
                          Subscription-based credits reset monthly according to
                          your account's billing cycle date.
                        </p>
                      </div>
                      <div className="flex items-start gap-2">
                        <div className="flex-shrink-0 w-1 h-1 mt-2 rounded-full bg-white/50"></div>
                        <p className="leading-[22px] font-['Inter']">
                          Any top-up credits you purchase will remain in your
                          account permanently.
                        </p>
                      </div>
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-[#F5CC62] text-[28px] leading-[32px] font-semibold">
                {credits.toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </span>
              <img alt="Balance Coin" src={CopperCoin} className="w-6 h-6" />
            </div>
          </div>
          <TierBadge tier={tier} />
        </div>

        {creditResponse ? (
          <div className="bg-[#FFFFFF0A] rounded-[8px] p-4 flex flex-col gap-4 mt-4">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center">
                <span className="text-[#FFFFFF50] text-[15px] font-['Inter'] font-medium">
                Monthly Credits
                </span>
                <Tooltip delayDuration={0}>
                  <TooltipTrigger asChild>
                    <img src={InfoSVG} alt="Info" className="w-4 h-4 ml-1 cursor-pointer" />
                  </TooltipTrigger>
                  <TooltipContent
                    className="px-3 py-2 rounded-[8px] bg-[#272829]/95 backdrop-blur-md border-[#2E2F34] text-white shadow-lg"
                    side="bottom"
                    align="end"
                    style={{ backdropFilter: "blur(20px)" }}
                  >
                    <span className="text-[15px] font-['Inter'] font-medium text-[#FFFFFF99] tracking-[-0.2px]">
                      {creditResponse.subscription.status == "initiated_cancellation" ? "Expires on" : "Refreshes on"} <span className="text-[#FFFFFF] font-['Inter'] font-medium">{formatDateOnly(creditResponse.monthly_credits_refresh_date)}</span>
                    </span>
                  </TooltipContent>
                </Tooltip>
              </div>
              <span className="text-[#FFFFFF50] text-[15px] font-['Inter'] font-medium">
                <span
                  className={cn(
                    "text-[15px] font-['Inter'] font-medium",
                    creditResponse.monthly_credits_balance > 0 &&
                      "text-[#F5CC62]"
                  )}
                >
                  {creditResponse.monthly_credits_balance.toFixed(2)}
                </span>{" "}
                / {creditResponse.subscription.monthly_credit_limit}
              </span>
            </div>
            { !isMobile && <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-[6px]">
                <span className="text-[#FFFFFF50] text-[15px] font-['Inter'] font-medium">
                  {tier === "free" ?  "Bonus Credits" : "Top up Credits"}
                </span>
              </div>
              <span
                className={cn(
                  "text-[#FFFFFF50] text-[15px] font-['Inter'] font-medium",
                  creditResponse.top_up_credit_balance > 0 &&
                    creditResponse.monthly_credits_balance <= 0 &&
                    "text-[#F5CC62]"
                )}
              >
                {creditResponse.top_up_credit_balance.toFixed(2)}
              </span>
            </div>}
          </div>
        ) : null}

        <div className="min-w-full border-[2px]  border-t border-dashed border-b-0 border-l-0 border-r-0 mt-[24px] mb-[24px]"></div>
        <FreeTierMessage tier={tier} />

        <ActionButtons tier={tier} isMobile={isMobile} />
      </div>
    </TooltipProvider>
  );
}
