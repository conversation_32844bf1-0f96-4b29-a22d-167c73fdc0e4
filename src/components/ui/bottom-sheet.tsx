import * as React from "react"
import { Button } from "@/components/ui/button"
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDes<PERSON>,
  DrawerFooter,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON>le,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { cn } from "@/lib/utils";

interface BottomSheetProps {
  trigger: React.ReactNode
  title?: string
  description?: string
  children: React.ReactNode
  footer?: React.ReactNode
  open?: boolean
  onOpenChange?: (open: boolean) => void
  maxWidth?: string
  showDefaultFooter?: boolean
  onSubmit?: () => void
  onCancel?: () => void
  submitText?: string
  cancelText?: string
  submitDisabled?: boolean
  contentPadding?: string
  customerHeader?: React.ReactNode;
  triggerColor?: string
}

export function BottomSheet({
  trigger,
  title,
  description,
  children,
  footer,
  open,
  onOpenChange,
  maxWidth = "max-w-sm",
  contentPadding = "p-3",
  showDefaultFooter = true,
  onSubmit,
  onCancel,
  submitText = "Submit",
  cancelText = "Cancel",
  submitDisabled = false,
  customerHeader,
  triggerColor,
}: BottomSheetProps) {
  const handleSubmit = () => {
    onSubmit?.()
  }

  const handleCancel = () => {
    onCancel?.()
  }

  return (
    <Drawer open={open} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>
        {trigger}
      </DrawerTrigger>
      <DrawerContent>
        <div className={`mx-auto w-full ${maxWidth}`}>
          {customerHeader}
          {(title || description) && (
            <DrawerHeader>
              {title && <DrawerTitle>{title}</DrawerTitle>}
              {description && <DrawerDescription>{description}</DrawerDescription>}
            </DrawerHeader>
          )}
          
          <div className={cn("pb-0", contentPadding)}>
            {children}
          </div>

          {(footer || showDefaultFooter) && (
            <DrawerFooter>
              {footer || (
                <>
                  <Button onClick={handleSubmit} disabled={submitDisabled}>
                    {submitText}
                  </Button>
                  <DrawerClose asChild>
                    <Button variant="outline" onClick={handleCancel}>
                      {cancelText}
                    </Button>
                  </DrawerClose>
                </>
              )}
            </DrawerFooter>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  )
}
