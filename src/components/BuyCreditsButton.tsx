import { useState } from 'react';
import { BuyCreditsModal } from './modals/BuyCreditsModal';
import PlusIcon from "@/assets/PlusIcon.svg";
import { useCredits } from '@/contexts';
import { UpgradeTierModal } from './modals/UpgradeTierModal';

interface BuyCreditsButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'subtle';
  size?: 'sm' | 'md' | 'lg';
  label?: string;
}

export function BuyCreditsButton({
  className = '',
  variant = 'default',
  size = 'md',
  label = 'Buy More Credits'
}: BuyCreditsButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpgradeModalOpen, setIsUpgradeModalOpen] = useState(false);

  const {tier} = useCredits();

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if(tier === "free") {
      setIsUpgradeModalOpen(true);
    } else {
      setIsModalOpen(true);
    }
  };

  const getButtonStyle = () => {
    switch (variant) {
      case 'outline':
        return 'border border-[#F3CA5F] text-[#F3CA5F] hover:bg-[#F3CA5F]/10';
      case 'subtle':
        return 'bg-[#F3CA5F]/10 text-[#F3CA5F] hover:bg-[#F3CA5F]/20';
      default:
        return 'bg-[#F3CA5F] text-black hover:bg-[#E7A93C]';
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2';
    }
  };

  return (
    <>
      <button
        type="button"
        onClick={handleClick}
        className={`${getButtonStyle()} ${getButtonSize()} hidden px-4 py-3 rounded-lg justify-between font-semibold tracking-[-0.2px] md:flex items-center gap-2 ${className}`}
      >
        {label}
        <img src={PlusIcon} alt="right arrow" className="w-6 h-6" />

      </button>

      <button
        type="button"
        onClick={handleClick}
        className={"flex md:hidden w-[90%]  mt-2 ml-3 bg-[#F3CA5F] text-black hover:bg-[#E7A93C] p-2 rounded-lg justify-between font-semibold tracking-[-0.2px] items-center gap-2 "}
      >
        {label}
        <img src={PlusIcon} alt="right arrow" className="w-6 h-6" />

      </button>

      <BuyCreditsModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
      />

      <UpgradeTierModal
        isOpen={isUpgradeModalOpen}
        onOpenChange={setIsModalOpen}
      />
    </>
  );
}
