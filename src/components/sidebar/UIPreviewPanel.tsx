import { RefreshCcw, Loader2, Download } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import AgentSleepingSVG from "@/assets/agentsleeping.svg";
import AgentBell from "@/assets/fluent-emoji_bell.svg";
import AlertFillSVG from "@/assets/alert-fill.svg";
import { cn } from "@/lib/utils";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import CloseSVG from "@/assets/panels/close.svg";
import LinkSVG from "@/assets/panels/link.svg";
import ShareSVG from "@/assets/panels/share.svg";
import ShareGraySVG from "@/assets/panels/share-gray.svg";
import RefreshSvg from "@/assets/panels/refresh.svg";
import PhoneIconSVG from "@/assets/panels/phone.svg";
import { ShareModal } from "@/components/sidebar/ShareModal";
import { useCodeDownload } from "@/hooks/useCodeDownload";
import { BottomSheet } from "../ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import { trackFeatureUsage } from "@/services/postHogService";
import MobileMock from "@/components/icons/IPhoneMock";
import { URL_LINKS } from "@/constants/constants";
import RefreshIcon from "../icons/RefreshIcon";
import LinkIcon from "../icons/LinkIcon";
import ShareIcon from "../icons/Actions/ShareIcon";
import ExpoIcon from "@/assets/expo.svg";

// Share Button Components
interface ShareButtonProps {
  onClick: () => void;
}

const ExpoShareButton: React.FC<ShareButtonProps> = ({ onClick }) => (
  <button
    type="button"
    onClick={onClick}
    className="flex items-center justify-center h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] rounded-[6px] gap-1 p-1 pr-2"
  >
    <img src={ShareGraySVG} alt="Share app" className="w-5 h-5" />
  </button>
);

const NonExpoShareButton: React.FC<ShareButtonProps> = ({ onClick }) => (
  <button
    type="button"
    onClick={onClick}
    className="flex items-center justify-center h-8 bg-white hover:bg-white/90 rounded-[6px] gap-1 p-1 pr-2"
  >
    <img src={ShareSVG} alt="Share app" className="w-5 h-5" />
    <span className="text-[#0F0F10] font-semibold text-[14px]">Share</span>
  </button>
);

interface PortMapping {
  service: string;
  hostPort: number;
  containerPort: number;
}

interface UrlPreviewPanelProps {
  isOpen: boolean;
  onClose: () => void;
  previewUrl: string;
  shareableLink: string;
  portMappings?: PortMapping[];
  podIsPaused?: boolean;
  showCase?: boolean;
  onResumePod?: () => void;
  isFromGithub?: any;
  shouldReloadIframe?: boolean;
  onIframeReloaded?: () => void;
  isResizing?: boolean;
  jobId?: string;
  archivedPod?: boolean;
  env_image?: string;
  handleOpenQrCodeModal?: () => void;
}

export function UrlPreviewPanel({
  isOpen,
  onClose,
  previewUrl,
  shareableLink,
  showCase,
  podIsPaused = false,
  onResumePod,
  isFromGithub,
  shouldReloadIframe = false,
  onIframeReloaded,
  isResizing = false,
  jobId = "",
  archivedPod = false,
  env_image = "",
  handleOpenQrCodeModal,
}: UrlPreviewPanelProps) {
  const [iframeLoading, setIframeLoading] = useState(true);
  const [loadingFailed, setLoadingFailed] = useState(false);
  const [loadingErrorMessage, setLoadingErrorMessage] = useState(
    "The preview could not be loaded. Please try again."
  );
  const [showShareModal, setShowShareModal] = useState(false);
  const [isCheckingHealth, setIsCheckingHealth] = useState(false);
  const [healthCheckAttempt, setHealthCheckAttempt] = useState(0);
  const [showIframe, setShowIframe] = useState(false);

  const { isDownloading, handleDownload } = useCodeDownload();

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [key, setKey] = useState(0);
  const healthCheckAbortRef = useRef<AbortController | null>(null);
  const iframeDelayTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isHealthCheckRunningRef = useRef(false);
  const { isMobile } = useScreenSize();

  const isExpoImage = env_image?.includes("expo");

  useEffect(() => {
    // console.log("Preview URL:", previewUrl);
    trackFeatureUsage("preview_on_mount", {
      jobId,
      isVisible: true,
      previewUrl,
      iframeLoading,
      loadingFailed,
      loadingErrorMessage,
    });
  }, []);

  const MAX_HEALTH_CHECK_ATTEMPTS = 5;
  const HEALTH_CHECK_DELAY_MS = 1000; // 1 second delay between attempts (after previous completes)
  const IFRAME_DELAY_MS = 100; // 1 second delay after successful health check

  // Handle iframe reload when URL or panel state changes
  useEffect(() => {
    if (isOpen && previewUrl && !podIsPaused) {
      handleRefresh();
    }

    return () => {
      clearAllTimers();
    };
  }, [isOpen, previewUrl, podIsPaused]);

  // Handle iframe reload when requested (after wakeup)
  useEffect(() => {
    if (shouldReloadIframe && previewUrl && !podIsPaused) {
      handleRefresh();

      // Notify parent that reload has been initiated
      if (onIframeReloaded) {
        onIframeReloaded();
      }
    }
  }, [shouldReloadIframe, previewUrl, podIsPaused]);

  // Clear all timers and abort ongoing requests to prevent memory leaks
  const clearAllTimers = () => {
    if (healthCheckAbortRef.current) {
      healthCheckAbortRef.current.abort();
      healthCheckAbortRef.current = null;
    }

    if (iframeDelayTimeoutRef.current) {
      clearTimeout(iframeDelayTimeoutRef.current);
      iframeDelayTimeoutRef.current = null;
    }

    isHealthCheckRunningRef.current = false;
  };

  const handleOpenInNewTab = () => {
    if (shareableLink || previewUrl) {
      trackFeatureUsage("open_preview_in_new_tab_clicked", {
        jobId,
        isVisible: true,
        previewUrl,
        iframeLoading,
        loadingFailed,
        loadingErrorMessage,
      });
      window.open(showCase ? previewUrl : shareableLink, "_blank");
    }
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  // Health check function to verify if the URL is ready
  const performHealthCheck = async (
    url: string,
    attempt: number,
    abortController: AbortController
  ): Promise<boolean> => {
    try {
      console.log(
        `Starting health check attempt ${attempt}/${MAX_HEALTH_CHECK_ATTEMPTS}`
      );

      const response = await fetch(url, {
        method: "GET",
        mode: "no-cors",
        cache: "no-cache",
        signal: abortController.signal,
      });

      console.log(`Health check attempt ${attempt} completed successfully`);

      // For no-cors mode, we can't check the actual status
      // But if the request doesn't throw an error, the service is likely up
      return true;
    } catch (error: any) {
      if (error.name === "AbortError") {
        console.log(`Health check attempt ${attempt} was aborted`);
        return false;
      }

      console.log(`Health check attempt ${attempt} failed:`, error);
      return false;
    }
  };

  // Start health check process with sequential requests
  const startHealthCheck = async () => {
    if (!previewUrl || isHealthCheckRunningRef.current) return;

    // Clear any existing health check
    clearAllTimers();

    // Create new abort controller for this health check session
    healthCheckAbortRef.current = new AbortController();
    isHealthCheckRunningRef.current = true;

    setIsCheckingHealth(true);
    setHealthCheckAttempt(1);
    setShowIframe(false);
    setLoadingFailed(false);
    setIframeLoading(true);

    let currentAttempt = 1;

    const performSequentialHealthChecks = async () => {
      while (
        currentAttempt <= MAX_HEALTH_CHECK_ATTEMPTS &&
        isHealthCheckRunningRef.current
      ) {
        // Update the attempt number in state
        setHealthCheckAttempt(currentAttempt);

        const isHealthy = await performHealthCheck(
          previewUrl,
          currentAttempt,
          healthCheckAbortRef.current!
        );

        // Check if we were aborted during the request
        if (!isHealthCheckRunningRef.current) {
          return;
        }

        if (isHealthy) {
          console.log("Health check successful, preparing to show iframe");
          setIsCheckingHealth(false);
          isHealthCheckRunningRef.current = false;

          // Wait 1 second before showing the iframe
          iframeDelayTimeoutRef.current = setTimeout(() => {
            if (isHealthCheckRunningRef.current === false) {
              // Double check we haven't been aborted
              setShowIframe(true);
              setKey((prevKey) => prevKey + 1); // Force iframe recreation
            }
          }, IFRAME_DELAY_MS);

          trackFeatureUsage(`preview_healthcheck_successful`, {
            jobId,
            isVisible: true,
            previewUrl,
            iframeLoading,
            loadingFailed,
            loadingErrorMessage,
          });

          return;
        }

        // If this was the last attempt, show error
        if (currentAttempt >= MAX_HEALTH_CHECK_ATTEMPTS) {
          console.log("All health check attempts failed");
          setIsCheckingHealth(false);
          setLoadingFailed(true);
          setIframeLoading(false);
          setLoadingErrorMessage(
            `Service is not responding after ${MAX_HEALTH_CHECK_ATTEMPTS} attempts. The service might be starting up or experiencing issues.`
          );
          isHealthCheckRunningRef.current = false;
          trackFeatureUsage("preview_health_check_failed", {
            jobId,
            isVisible: true,
            previewUrl,
            iframeLoading,
            loadingFailed,
            loadingErrorMessage,
          });
          return;
        }

        // Wait before next attempt (only if we're still running)
        if (isHealthCheckRunningRef.current) {
          console.log(
            `Waiting ${HEALTH_CHECK_DELAY_MS}ms before next attempt...`
          );
          await new Promise((resolve) =>
            setTimeout(resolve, HEALTH_CHECK_DELAY_MS)
          );
        }

        currentAttempt++;
      }
    };

    await performSequentialHealthChecks();
  };

  const handleRefresh = () => {
    if (!previewUrl) return;

    console.log("Starting health check for URL:", previewUrl);

    // Reset all states
    setIframeLoading(true);
    setLoadingFailed(false);
    setShowIframe(false);
    setLoadingErrorMessage(
      "The preview could not be loaded. Please try again."
    );

    clearAllTimers();
    startHealthCheck();
  };

  // Render the preview content
  const renderPreviewContent = () => {
    if (podIsPaused && !showCase) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center">
          <div className="w-full z-[20] px-[2rem] gap-6 rounded-[1rem] md:rounded-bl-none md:rounded-br-none md:pt-[2rem] md:pb-[3rem] flex flex-col justify-between items-center">
            <div className="flex flex-col items-center gap-5">
              <img
                src={AgentSleepingSVG}
                className="min-w-[100px] min-h-[100px]"
                alt="Agent Sleeping"
              />
              <div className="flex flex-col items-center gap-3">
                <span className="text-[#C4C4CC] text-[20px]">
                  {archivedPod
                    ? "Preview archived"
                    : "Preview paused - Agent is sleeping"}
                </span>
                {/* Show different messages based on archived status and GitHub status */}
                {archivedPod ? (
                  <span className="text-[#7b7b80] text-sm">
                    This job has been archived. You can download your code and
                    create a new job. Need help? Visit our{" "}
                    <a
                      href={URL_LINKS.helpCenter}
                      className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-semibold underline underline-offset-1"
                    >
                      Help Center
                    </a>
                  </span>
                ) : isFromGithub ? (
                  <span className="text-[#7b7b80] text-sm">
                    Apologies, the task has expired due to inactivity. We may
                    not be able to recover this at this moment. Please start a
                    new task using the same github repo. Reach out to us at{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="text-[#ACACB2] font-extrabold underline underline-offset-1"
                    >
                      <EMAIL>
                    </a>{" "}
                    for any assistance.
                  </span>
                ) : (
                  <span className="text-[#7b7b80] text-sm">
                    If you are having trouble accessing your work, Please
                    contact support at{" "}
                    <a
                      href="mailto:<EMAIL>"
                      className="bg-gradient-to-r font-['Inter'] from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-semibold underline underline-offset-1"
                    >
                      <EMAIL>
                    </a>
                  </span>
                )}
              </div>
            </div>
            {archivedPod ? (
              <button
                type="button"
                onClick={() => handleDownload(jobId)}
                disabled={isDownloading || !jobId}
                className="bg-[#FCB94920] max-w-[200px] justify-center disabled:opacity-25 p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
              >
                <Download className="w-5 h-5 text-[#FCB949]" />
                <span className="bg-gradient-to-r text-nowrap from-[#FCB949] to-[#E28C37] text-transparent text-[12px] md:text-[16px] bg-clip-text font-medium">
                  {isDownloading ? "Downloading..." : "Download Your Code"}
                </span>
              </button>
            ) : isFromGithub ? null : (
              <button
                type="button"
                onClick={onResumePod}
                className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 md:min-w-[200px] hover:bg-[#FCB94930] transition-colors duration-200"
              >
                <img src={AgentBell} alt="Wake icon" className="w-5 h-5" />
                <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                  Wake up the Agent
                </span>
              </button>
            )}
          </div>
        </div>
      );
    }

    if (previewUrl && isOpen) {
      return (
        <div className="relative w-full h-full">
          {/* Health Check Loading State */}
          {isCheckingHealth && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
              <div className="flex flex-col items-center">
                <Loader2 className="w-8 h-8 text-[#5FD3F3] animate-spin mb-4" />
                <p className="text-[#939399]">Loading preview...</p>
              </div>
            </div>
          )}

          {/* Iframe Loading State */}
          {showIframe && iframeLoading && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
              <div className="flex flex-col items-center">
                <Loader2 className="w-8 h-8 text-[#5FD3F3] animate-spin mb-4" />
                <p className="text-[#939399]">Loading preview...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {loadingFailed && (
            <div className="absolute inset-0 flex items-center justify-center bg-[#0F0F10]">
              <div className="flex flex-col items-center max-w-md gap-6 text-center">
                <img src={AlertFillSVG} alt="Alert" className="w-12 h-12" />
                <div className="flex flex-col gap-2">
                  <p className="text-[#C4C4CC] text-[24px] font-medium">
                    Preview failed to load
                  </p>
                  <p className="text-[#939399]">{loadingErrorMessage}</p>
                </div>
                <button
                  type="button"
                  onClick={handleRefresh}
                  className="bg-[#FCB94920] p-[10px] rounded-[6px] flex items-center gap-2 hover:bg-[#FCB94930] transition-colors duration-200"
                >
                  <RefreshCcw className="w-5 h-5 text-[#FCB949]" />
                  <span className="bg-gradient-to-r from-[#FCB949] to-[#E28C37] text-transparent bg-clip-text font-medium">
                    Retry Health Check
                  </span>
                </button>
              </div>
            </div>
          )}

          {/* Iframe */}
          {showIframe && !isExpoImage && (
            <iframe
              ref={iframeRef}
              key={key}
              src={previewUrl}
              className="flex-1 w-full h-full overflow-y-auto border-0 rounded-sm"
              style={{
                pointerEvents: isResizing ? "none" : "auto",
                transform: "scale(1)",
                transformOrigin: "top left",
              }}
              onLoad={() => {
                setIframeLoading(false);
                trackFeatureUsage("preview_iframe_loaded", {
                  jobId,
                  isVisible: true,
                  previewUrl,
                  iframeLoading,
                  loadingFailed,
                  loadingErrorMessage,
                });
              }}
              onError={(e) => {
                console.error("Iframe failed to load:", e);
                setIframeLoading(false);
                setLoadingFailed(true);
                setLoadingErrorMessage(
                  "Failed to load the preview after successful health check. Please try refreshing."
                );
                trackFeatureUsage("preview_iframe_loading_failed", {
                  jobId,
                  isVisible: true,
                  previewUrl,
                  iframeLoading,
                  loadingFailed,
                  loadingErrorMessage,
                });
              }}
              title="URL Preview"
              sandbox="allow-scripts allow-same-origin allow-forms"
            />
          )}

          {showIframe && isExpoImage && !isMobile && (
            <div className="flex flex-1 justify-center items-center w-full h-full bg-[url('/bg_grid.png')] bg-repeat">
              <MobileMock className="w-[340px] h-[700px]">
                <iframe
                  ref={iframeRef}
                  key={key}
                  src={previewUrl}
                  className="flex-1 w-full h-full overflow-y-auto border-0"
                  style={{
                    pointerEvents: isResizing ? "none" : "auto",
                    transform: "scale(1)",
                    transformOrigin: "top left",
                  }}
                  onLoad={() => {
                    setIframeLoading(false);
                    trackFeatureUsage("preview_iframe_loaded", {
                      jobId,
                      isVisible: true,
                      previewUrl,
                      iframeLoading,
                      loadingFailed,
                      loadingErrorMessage,
                    });
                  }}
                  onError={(e) => {
                    console.error("Iframe failed to load:", e);
                    setIframeLoading(false);
                    setLoadingFailed(true);
                    setLoadingErrorMessage(
                      "Failed to load the preview after successful health check. Please try refreshing."
                    );
                    trackFeatureUsage("preview_iframe_loading_failed", {
                      jobId,
                      isVisible: true,
                      previewUrl,
                      iframeLoading,
                      loadingFailed,
                      loadingErrorMessage,
                    });
                  }}
                  title="URL Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms"
                />
              </MobileMock>
            </div>
          )}

          {showIframe && isExpoImage && isMobile && (
            <div className="flex flex-1 justify-center items-center w-full h-full bg-[url('/bg_grid.png')] bg-repeat">
              <iframe
                ref={iframeRef}
                key={key}
                src={previewUrl}
                className="flex-1 w-full h-full overflow-y-auto border-0"
                style={{
                  pointerEvents: isResizing ? "none" : "auto",
                  transform: "scale(1)",
                  transformOrigin: "top left",
                }}
                onLoad={() => {
                  setIframeLoading(false);
                  trackFeatureUsage("preview_iframe_loaded", {
                    jobId,
                    isVisible: true,
                    previewUrl,
                    iframeLoading,
                    loadingFailed,
                    loadingErrorMessage,
                  });
                }}
                onError={(e) => {
                  console.error("Iframe failed to load:", e);
                  setIframeLoading(false);
                  setLoadingFailed(true);
                  setLoadingErrorMessage(
                    "Failed to load the preview after successful health check. Please try refreshing."
                  );
                  trackFeatureUsage("preview_iframe_loading_failed", {
                    jobId,
                    isVisible: true,
                    previewUrl,
                    iframeLoading,
                    loadingFailed,
                    loadingErrorMessage,
                  });
                }}
                title="URL Preview"
                sandbox="allow-scripts allow-same-origin allow-forms"
              />
            </div>
          )}
        </div>
      );
    }

    return null;
  };

  // Render the header with controls
  const renderHeader = () => (
    <div className="p-4 px-0 md:p-4 bg-[#111112] md:bg-transparent flex items-center justify-between border-b border-[#242424]/60">
      <div className="text-[#939399] font-['Brockmann'] text-[15px] md:text-[18px] font-medium leading-[24px]">
        App Preview
      </div>
      <div className="flex items-center gap-2">
        <TooltipProvider>
          {isExpoImage && (
            <button
              type="button"
              onClick={handleOpenQrCodeModal}
              className="flex items-center justify-center h-8 bg-white hover:bg-white/90 rounded-[6px] gap-1 p-1 pr-2"
            >
              <img
                src={PhoneIconSVG}
                alt="Phone Icon app"
                className="w-5 h-5"
              />
              <span className="text-[#0F0F10] font-semibold md:text-[14px]">
                Preview on device
              </span>
            </button>
          )}
          {previewUrl && !isExpoImage && (
            <NonExpoShareButton onClick={handleShareClick} />
          )}
          {previewUrl && (
            <Tooltip delayDuration={100}>
              <TooltipTrigger asChild>
                <button
                  type="button"
                  onClick={handleRefresh}
                  className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                >
                  <RefreshIcon
                    width={20}
                    height={20}
                    spinning={iframeLoading}
                  />
                </button>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="bg-[#DDDDE6] text-black border-0"
                style={{ fontFamily: "Inter", fontWeight: 500 }}
              >
                Refresh
              </TooltipContent>
            </Tooltip>
          )}
          {previewUrl && isExpoImage && (
            <ExpoShareButton onClick={handleShareClick} />
          )}
          {previewUrl && (
            <Tooltip delayDuration={100}>
              <TooltipTrigger asChild>
                <button
                  type="button"
                  onClick={handleOpenInNewTab}
                  className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
                >
                  <img
                    src={LinkSVG}
                    alt="Open in new tab"
                    className="w-5 h-5"
                  />
                </button>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                className="bg-[#DDDDE6] text-black border-0"
                style={{ fontFamily: "Inter", fontWeight: 500 }}
              >
                Open in new tab
              </TooltipContent>
            </Tooltip>
          )}
          <Tooltip delayDuration={100}>
            <TooltipTrigger asChild>
              <button
                type="button"
                onClick={onClose}
                className="w-8 h-8 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[6px]"
              >
                <img src={CloseSVG} alt="Close" className="w-5 h-5" />
              </button>
            </TooltipTrigger>
            <TooltipContent
              side="bottom"
              className="bg-[#DDDDE6] text-black border-0"
              style={{ fontFamily: "Inter", fontWeight: 500 }}
            >
              Close
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );

  // Render the header with controls
  const renderMobileHeader = () => (
    <div className="p-4 md:p-4 bg-[#111112] md:bg-transparent flex items-center justify-between border-b border-[#242424]/60">
      <div className="flex justify-between w-full items-center gap-2">
        {previewUrl && (
          <button
            type="button"
            onClick={handleShareClick}
            className="flex items-center justify-center h-8 bg-[#FFFFFF0D] hover:bg-[#FFFFFF14] rounded-[26px] gap-1 px-3 py-2"
          >
            <ShareIcon width={20} height={20} color="#DCDCE5" />
            <span className="text-[#DCDCE5] font-semibold text-[14px]">
              Share
            </span>
          </button>
        )}
        <div className="text-[#fff] font-['Brockmann'] text-[18px] font-medium leading-[24px]">
          Preview
        </div>
        <div className="flex items-center gap-2">
          {previewUrl && (
            <button
              type="button"
              onClick={handleRefresh}
              className="w-9 h-9 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[32px]"
            >
              <RefreshIcon
                width={22}
                height={22}
                spinning={iframeLoading}
                fill="#DCDCE5"
              />
            </button>
          )}
          {previewUrl && (
            <button
              type="button"
              onClick={handleOpenInNewTab}
              className="w-9 h-9 bg-[#FFFFFF0A] hover:bg-[#FFFFFF14] flex items-center justify-center rounded-[32px]"
            >
              <LinkIcon color="#fff" size={22} />
            </button>
          )}
        </div>
      </div>
    </div>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <>
        <BottomSheet
          trigger={<div />}
          open={isOpen}
          onOpenChange={(open) => {
            if (!open) {
              onClose();
            }
          }}
          maxWidth="max-w-full"
          showDefaultFooter={false}
          customerHeader={
            <div className="w-full flex justify-center items-center z-[51] absolute mx-auto top-[-56px]">
              <button
                className="bg-white w-[240px] h-12 rounded-[26px] flex items-center justify-between px-4 py-3"
                onClick={handleOpenQrCodeModal}
              >
                <img
                  src={ExpoIcon}
                  alt="Expo Icon"
                  className="w-fit h-[20px]"
                />
                <span className="text-[#000020] font-bold text-base">
                  Preview in Expo
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M9 17C9 16.4477 8.5523 16 8 16H6C5.44771 16 5 16.4477 5 17V19C5 19.5523 5.44771 20 6 20H8C8.5523 20 9 19.5523 9 19V17ZM13 21V20.9902C13 20.438 13.4477 19.9902 14 19.9902C14.5523 19.9902 15 20.438 15 20.9902V21C15 21.5523 14.5523 22 14 22C13.4477 22 13 21.5523 13 21ZM17 21V19C17 18.4477 17.4477 18 18 18H20C20.5523 18 21 18.4477 21 19C21 19.5523 20.5523 20 20 20H19V21C19 21.5523 18.5523 22 18 22C17.4477 22 17 21.5523 17 21ZM13 15C13 14.4477 13.4477 14 14 14C14.5523 14 15 14.4477 15 15V16H16C16.5523 16 17 16.4477 17 17C17 17.5523 16.5523 18 16 18H14C13.4477 18 13 17.5523 13 17V15ZM20 14C20.5523 14 21 14.4477 21 15C21 15.5523 20.5523 16 20 16H18C17.4477 16 17 15.5523 17 15C17 14.4477 17.4477 14 18 14H20ZM9 7C9 6.44771 8.55229 6 8 6H6C5.44771 6 5 6.44771 5 7V9C5 9.55229 5.44771 10 6 10H8C8.55229 10 9 9.55229 9 9V7ZM19 7C19 6.44771 18.5523 6 18 6H16C15.4477 6 15 6.44771 15 7V9C15 9.5523 15.4477 10 16 10H18C18.5523 10 19 9.5523 19 9V7ZM11 19C11 20.6569 9.65685 22 8 22H6C4.34315 22 3 20.6569 3 19V17C3 15.3431 4.34315 14 6 14H8C9.65685 14 11 15.3431 11 17V19ZM11 9C11 10.6569 9.65685 12 8 12H6C4.34315 12 3 10.6569 3 9V7C3 5.34315 4.34315 4 6 4H8C9.65685 4 11 5.34315 11 7V9ZM21 9C21 10.6568 19.6569 12 18 12H16C14.3431 12 13 10.6568 13 9V7C13 5.34315 14.3431 4 16 4H18C19.6569 4 21 5.34315 21 7V9Z"
                    fill="black"
                  />
                </svg>
              </button>
            </div>
          }
          contentPadding="p-0"
        >
          <div className="h-[85dvh] border-t border-t-2 border-white mb-0 flex flex-col bg-[#111112] rounded-t-2xl overflow-hidden">
            <div className="flex-1 overflow-hidden">
              {renderPreviewContent()}
            </div>
            {renderMobileHeader()}
          </div>
        </BottomSheet>

        {/* Share Modal */}
        <ShareModal
          isOpen={showShareModal}
          onOpenChange={setShowShareModal}
          shareableLink={showCase ? previewUrl : shareableLink}
          fromShowcase={showCase}
        />
      </>
    );
  }

  // For desktop, use the original layout
  return (
    <div
      className={cn(
        `w-full h-full bg-[#111112] max-md:absolute max-md:inset-0`,
        isOpen ? "block" : "hidden"
      )}
    >
      <div className="flex flex-col h-full">
        {renderHeader()}
        <div className="flex-1 p-0 overflow-hidden">
          {renderPreviewContent()}
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onOpenChange={setShowShareModal}
        shareableLink={showCase ? previewUrl : shareableLink}
        fromShowcase={showCase}
      />
    </div>
  );
}
