import { buttonVariants } from "@/components/ui/button";
import { X, File } from "lucide-react";
import CopyButton from "../CopyButton";
import { cn } from "@/lib/utils";
import LinkSVG from "@/assets/Link.svg";
import CopyableSVG from "@/assets/Copyable.svg";
import Github from "@/assets/menu-icons/github.svg";
import BranchSVG from "@/assets/branch2.svg";
import GreenBranch from "@/assets/GreenBranch.svg";
import { useIsEmergentUser } from "@/hooks/useIsEmergentUser";
import { useDeploy } from "@/hooks/useDeploy";
import AssetCard from "../upload/asset-card";
import ArrowNext from "@/assets/upload/arrow-next.svg";
import { useState, useEffect, useCallback, useRef } from "react";
import { agentApi } from "@/services/agentApi";
import { downloadFileFromUrl } from "@/lib/utils/downloadFile";
import { Artifact } from "@/types/artifact";
import { useToast } from "@/hooks/use-toast";
import NoAssets from "@/assets/upload/no-assets.svg"
import { isAutoBudgetUpdateEnabled, useIsUploadAssetEnabled } from "@/services/postHogService";
import { BottomSheet } from "@/components/ui/bottom-sheet";
import useScreenSize from "@/hooks/useScreenSize";
import { useBudget } from "@/hooks/useBudget";
import SilverCoin from "../icons/SilverCoin";
import UploadNormal from "@/assets/upload/upload_normal.svg";
import { useGetUserPromptQuery } from "@/store/api/promodeApiSlice";

// Helper function to format file size
const formatFileSize = (bytes: string | number) => {
  const size = typeof bytes === 'string' ? parseInt(bytes) : bytes;
  if (isNaN(size)) return 'Unknown size';

  const units = ['B', 'KB', 'MB', 'GB'];
  let unitIndex = 0;
  let fileSize = size;

  while (fileSize >= 1024 && unitIndex < units.length - 1) {
    fileSize /= 1024;
    unitIndex++;
  }

  return `${fileSize.toFixed(1)} ${units[unitIndex]}`;
};



interface ChatInfoPanelProps {
  isOpen: boolean;
  onClose: () => void;
  onOpenUploadAssetsModal: (files?: File[]) => void;
  asset_upload_enabled?: boolean;
  chatInfo: {
    modelName?: string;
    agentName?: string;
    containerStatus?: string;
    imageVersion?: string;
    containerName?: string;
    containerId?: string;
    user_prompt_id?: string;
    assignedPorts?: {
      service: string;
      hostPort: number;
      containerPort: number;
    }[];
    jobId?: string;
    isCloudFlow?: boolean;
    promptName?: string;
    promptVersion?: string;
    costLimit?: number;
    vscodeUrl?: string;
    vscodePassword?: string;
    targetRepo?: {
      branch: string;
      repo: string;
      owner: string;
      provider: string;
    } | null;
    sourceRepo?: {
      branch: string;
      repo: string;
      owner: string;
      provider: string;
    } | null;
  };
}



const InfoSection = ({
  title,
  value,
  copyable = false,
  maxChars,
  onClick,
  className,
  icon,
}: {
  title: string;
  value?: string;
  copyable?: boolean;
  maxChars?: number;
  onClick?: () => void;
  className?: string;
  icon?: string;
}) => {
  const displayValue =
    maxChars && value
      ? value.length > maxChars
        ? `${value.slice(0, maxChars)}...`
        : value
      : value;

  return (
    <div className="space-y-2">
      <div className="text-[#7B7B80] font-medium font-['Inter'] text-sm">
        {title}
      </div>
      <div
        className={cn("flex items-center justify-between gap-2", className)}
        onClick={onClick}
      >
        <div className="text-[#C4C4CC] font-['Inter'] text-base group-hover:text-[#2EBBE5] transition-colors duration-200">
          {displayValue || "—"}
        </div>
        {copyable && value && (
          <CopyButton
            tooltipEnabled={true}
            showTooltipOnHover={true}
            tooltipText="Copy"
            copiedTooltipText="Copied"
            showIcon={true}
            iconOnly={true}
            className="border-none bg-none"
            buttonClassName={buttonVariants({
              variant: "outline",
              size: "sm",
              className:
                "whitespace-nowrap rounded-lg  transition-all ease-in-out duration-200 border-white/20 border hover:bg-white/20 hover:text-white text-white/40",
            })}
            value={() => value}
            iconProps={{ size: 16 }}
            onCopy={async () => {
              await navigator.clipboard.writeText(value);
            }}
          />
        )}

        {icon && !copyable && (
          <div
            className="flex items-center justify-center min-w-[40px] min-h-[36px] cursor-pointer rounded-md hover:bg-white/10 transition-colors duration-200"
            onClick={onClick}
          >
            <img src={icon} alt="icon" className="w-4 h-4" />
          </div>
        )}
      </div>
    </div>
  );
};

export function ChatInfoPanel({
  isOpen,
  onClose,
  chatInfo,
  onOpenUploadAssetsModal,
}: ChatInfoPanelProps) {
  const { isMobile } = useScreenSize();
  const isEmergentUser = useIsEmergentUser();
  const { deployStatus, deployUrl, customDomainUrl, customDomain } = useDeploy(
    chatInfo.jobId
  );

  const [view, setView] = useState<"info" | "assets">("info");
  const [artifacts, setArtifacts] = useState<Artifact[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { budgetInfo } = useBudget({
    jobId: chatInfo.jobId,
  });

  const isUploadAssetEnabled = useIsUploadAssetEnabled();

  // Fetch user prompt details if user_prompt_id is present
  const { data: userPromptData } = useGetUserPromptQuery(
    chatInfo.user_prompt_id || '',
    {
      skip: !chatInfo.user_prompt_id, // Skip the query if no user_prompt_id
    }
  );

  // Fetch artifacts when panel opens or jobId changes
  const fetchArtifacts = useCallback(async () => {
    if (!chatInfo.jobId) return;

    setLoading(true);
    try {
      const fetchedArtifacts = await agentApi.getArtifacts(chatInfo.jobId);
      setArtifacts(fetchedArtifacts.reverse());
    } catch (error: any) {
      console.error('Error fetching artifacts:', error);
      toast({
        title: "Error",
        description: "Failed to load artifacts",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [chatInfo.jobId, toast]);

  useEffect(() => {
    if (isOpen && chatInfo.jobId) {
      fetchArtifacts();
    }
  }, [isOpen, chatInfo.jobId, fetchArtifacts]);

  // Handle artifact deletion
  const handleDelete = useCallback(async (artifactId: string, fileName: string) => {
    try {
      await agentApi.deleteArtifact(artifactId);
      setArtifacts(prev => prev.filter(artifact => artifact.id !== artifactId));
      toast({
        title: "Deleted",
        description: `${fileName} has been deleted`,
      });
    } catch (error: any) {
      console.error('Error deleting artifact:', error);
      toast({
        title: "Error",
        description: `Failed to delete ${fileName}`,
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle artifact download
  const handleDownload = useCallback(async (artifact: Artifact) => {
    try {
      const response = await agentApi.getArtifactDownloadUrl(artifact.id);
      await downloadFileFromUrl(response.download_url, artifact.name || 'download');
    } catch (error: any) {
      console.error('Error getting download URL:', error);
      toast({
        title: "Download failed",
        description: "Failed to get download link for this artifact",
        variant: "destructive",
      });
    }
  }, [toast]);

  // Handle file input change
  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileArray = Array.from(files);
      onOpenUploadAssetsModal(fileArray);
    }
    // Reset the input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [onOpenUploadAssetsModal]);

  // Handle upload button click
  const handleUploadClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // Shared header component
  const renderHeader = () => (
    <div className="p-4 md:px-6 md:py-5 bg-[#181818] md:bg-transparent flex items-center justify-between border-b border-[#242424]/60">
      {view == "info" && (
        <>
          <div className="text-[#939399] font-['Brockmann']  text-[15px]  md:text-[18px] font-medium leading-[24px]">
            Run Details
          </div>

          <button
            type="button"
            title="Close"
            onClick={onClose}
            className="w-10 h-10 flex items-center justify-center bg-[#FFFFFF05] hover:bg-[#FFFFFF1A] backdrop-blur-lg rounded-[10px]"
          >
            <X className="w-6 h-6 text-[#7B7B80]" />
          </button>
        </>
      )}

      {view == "assets" && (
        <>
          <div className="text-[#939399] font-['Brockmann'] flex items-center w-full justify-between text-[15px] gap-2  md:text-[18px] font-medium leading-[24px]">
            <div className="flex items-center gap-2">
              <div
                onClick={() => {
                  setView("info");
                }}
                className="w-8 h-8 hover:bg-[#FFFFFF10]  flex items-center cursor-pointer justify-center rounded-[8px] backdrop-blur-lg"
              >
                <img
                  src={ArrowNext}
                  alt="Arrow"
                  className="w-6 h-6 rotate-180"
                />
              </div>
              Assets ({artifacts.length})
            </div>

              <button
                  type="button"
                  onClick={handleUploadClick}
                  className="text-[16px] pr-4 radial-green text-[#80FFF9] flex justify-center items-center gap-2 p-2 pl-3 min-h-[36px] max-h-[36px] rounded-[10px] "
                >
                  <img src={UploadNormal} alt="Add" className="w-5 h-5" />
                  Upload Files
                </button>

            {/* {chatInfo.jobId && (
              <button
                type="button"
                onClick={fetchArtifacts}
                disabled={loading}
                className="w-8 h-8 hover:bg-[#FFFFFF06] flex items-center cursor-pointer justify-center rounded-[4px] backdrop-blur-lg text-[#E6E6E6] hover:text-white"
              >
                {loading ? <RefreshCcw className="w-4 h-4 animate-spin" /> : <RefreshCcw className="w-4 h-4 text-[#7b7979]" />}
              </button>
            )} */}
          </div>
        </>
      )}
    </div>
  );

  // Shared content component
  const renderContent = () => (
    <>
      {view == "info" && (
        <div className="flex-1 p-6 space-y-8 overflow-x-hidden overflow-y-auto">
          <div className="space-y-6">
            {isAutoBudgetUpdateEnabled() && (
              <div className="flex flex-col justify-between items-start bg-white/10 p-4 rounded-[8px]">
                <div className="flex items-center justify-between w-full">
                  <p className="text-[#B1B1B3] text-sm font-semibold">
                    Credits Spent
                  </p>
                  <SilverCoin />
                </div>
                <div className="h-8"></div>

                <p className="text-[#F2C75B] text-[24px] font-bold font-['Inter']">
                  {budgetInfo?.current_usage?.toFixed(4)}
                </p>
              </div>
            )}
            <div className="space-y-4">
              <InfoSection title="Model" value={chatInfo.modelName} />
              {(isEmergentUser || chatInfo.user_prompt_id) && (
                <InfoSection
                  title="Agent"
                  value={
                    chatInfo.user_prompt_id
                      ? (userPromptData?.prompt_name?.replaceAll("_", " ") || chatInfo.promptName || chatInfo.user_prompt_id)
                      : chatInfo.promptName
                  }
                />
              )}
            </div>

            <div className="space-y-4">
              <InfoSection
                title="Job ID"
                value={chatInfo.jobId}
                copyable
                icon={CopyableSVG}
              />
            </div>

            {deployStatus === "success" && <InfoBar title="DEPLOYMENT INFO" />}

            {deployStatus === "success" && (
              <div className="text-[#C4C4CC] font-['Inter'] text-base space-y-4 flex-col flex">
                <InfoSection
                  title="Live Link"
                  icon={LinkSVG}
                  value={deployUrl}
                  onClick={() => {
                    if (deployUrl) {
                      window.open(deployUrl, "_blank");
                    }
                  }}
                  className="cursor-pointer group"
                />
                {customDomainUrl && customDomain?.status === "verified" && (
                  <InfoSection
                    title="Custom Domain"
                    icon={LinkSVG}
                    value={customDomain.domain}
                    onClick={() => {
                      if (customDomainUrl) {
                        window.open(customDomainUrl, "_blank");
                      }
                    }}
                    className="cursor-pointer group"
                  />
                )}
              </div>
            )}

            {isUploadAssetEnabled && (
              <>
                <InfoBar title="ASSETS" />

                <div className="">
                  {loading ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="text-center text-[#666]">
                        <div className="animate-spin w-6 h-6 border-2 border-[#5FD3F3] border-t-transparent rounded-full mx-auto mb-2"></div>
                        <p className="text-sm">Loading assets...</p>
                      </div>
                    </div>
                  ) : artifacts.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <div className="text-center text-[#666] max-w-[450px]">
                        <img
                          src={NoAssets}
                          alt="No Assets"
                          className="h-12 mx-auto mb-4"
                        />
                        <p className="text-[14px] font-['Inter'] font-medium text-[#FFFFFF20]">
                          Upload assets or sync files to be used as sources
                        </p>
                      </div>
                      <button
                        type="button"
                        onClick={handleUploadClick}
                        className="w-full max-w-[180px] mt-5 radial-green text-[#80FFF9] flex justify-center items-center gap-2 p-3 py-2 rounded-[10px] "
                      >
                        Upload Assets
                      </button>
                    </div>
                  ) : (
                    <>
                      {artifacts.slice(0, 3).map((artifact) => (
                        <AssetCard
                          key={artifact.id}
                          title={artifact.name}
                          size={formatFileSize(artifact.file_size || 0)}
                          type={artifact.mime_type || ""}
                          url={artifact.public_url || ""}
                          visibility={artifact.visibility}
                          id={artifact.id}
                          onRemove={(id) => handleDelete(id, artifact.name)}
                          onDownload={() => handleDownload(artifact)}
                        />
                      ))}

                      <div className="flex items-center gap-4 mt-4">
                        <button
                          type="button"
                          onClick={handleUploadClick}
                          className="w-full radial-green text-[#80FFF9] flex justify-center items-center gap-2 p-3 py-2 rounded-[10px] "
                        >
                          <img src={UploadNormal} alt="Add" className="w-5 h-5" />
                          Upload Files
                        </button>
                        {artifacts.length > 3 && (
                          <button
                            type="button"
                            onClick={() => {
                              setView("assets");
                            }}
                            className="w-full radial-black-upload p-3 py-2  hover:opacity-80 text-[#FFFFFF66] flex justify-center items-center gap-2 rounded-[10px] "
                          >
                            View all ({artifacts.length})
                            <img
                              src={ArrowNext}
                              alt="Arrow"
                              className="w-5 h-5"
                            />
                          </button>
                        )}
                        
                      </div>
                    </>
                  )}
                </div>
              </>
            )}

            {chatInfo.vscodeUrl && <InfoBar title="VS CODE INFO" />}

            <div className="space-y-4">
              <div className="text-[#C4C4CC] font-['Inter'] text-base space-y-4 flex-col flex">
                <InfoSection
                  title="VSCode URL"
                  icon={LinkSVG}
                  value={`${chatInfo.vscodeUrl}`}
                  onClick={() => {
                    window.open(chatInfo.vscodeUrl, "_blank");
                  }}
                  className="cursor-pointer group"
                />
                <InfoSection
                  title="VSCode Password"
                  icon={CopyableSVG}
                  value={`${chatInfo.vscodePassword}`}
                  copyable
                />
              </div>
            </div>

            {(chatInfo.sourceRepo || chatInfo.targetRepo) && (
              <InfoBar title="GITHUB INFO" />
            )}

            {chatInfo.sourceRepo && (
              <div
                className="bg-[#FFFFFF0D] p-3 rounded-[8px] text-sm flex flex-col gap-2 font-['Inter'] font-medium cursor-pointer hover:opacity-80 transition-opacity duration-200"
                onClick={() => {
                  const repoUrl = `https://github.com/${chatInfo.sourceRepo?.owner}/${chatInfo.sourceRepo?.repo}/tree/${chatInfo.sourceRepo?.branch}`;
                  window.open(repoUrl, "_blank");
                }}
              >
                <span className="text-[#7B7B80] font-['Inter'] text-sm font-medium">
                  Source Repository
                </span>

                <div className="flex items-center gap-1">
                  <img src={Github} alt="Github" className="w-5 h-5" />
                  <span className="capitalize text-[#C4C4CC] text-nowrap truncate">
                    {chatInfo.sourceRepo?.owner} / {chatInfo.sourceRepo?.repo}
                  </span>
                  <div className="flex items-center gap-1 ml-2 text-nowrap">
                    <img src={BranchSVG} alt="Branch" />
                    <span className="text-[#DADEE5] text-nowrap truncate">
                      {chatInfo.sourceRepo?.branch}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {chatInfo.targetRepo && (
              <div
                className="bg-[#FFFFFF0D] p-3 rounded-[8px] text-sm flex flex-col gap-2 font-['Inter'] font-medium cursor-pointer hover:opacity-80 transition-opacity duration-200"
                onClick={() => {
                  const repoUrl = `https://github.com/${chatInfo.targetRepo?.owner}/${chatInfo.targetRepo?.repo}/tree/${chatInfo.targetRepo?.branch}`;
                  window.open(repoUrl, "_blank");
                }}
              >
                <span className="text-[#7B7B80] font-['Inter'] text-sm font-medium">
                  Most Recent Export
                </span>

                <div className="flex items-center gap-1">
                  <img src={Github} alt="Github" className="w-5 h-5" />
                  <span className="capitalize text-[#C4C4CC]">
                    {chatInfo.targetRepo?.owner} / {chatInfo.targetRepo?.repo}
                  </span>
                  <div className="flex items-center gap-1 ml-2">
                    <img src={GreenBranch} alt="Branch" />
                    <span className="text-[#2EE572]">
                      {chatInfo.targetRepo?.branch}
                    </span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {view == "assets" && (
        <div className="flex-1 p-6 pt-4 overflow-x-hidden overflow-y-auto">
          {!chatInfo.jobId ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-[#666]">
                <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No job selected</p>
                <p className="mt-1 text-sm">
                  Assets will appear here when you have an active job
                </p>
              </div>
            </div>
          ) : loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-[#666]">
                <div className="animate-spin w-8 h-8 border-2 border-[#5FD3F3] border-t-transparent rounded-full mx-auto mb-4"></div>
                <p>Loading assets...</p>
              </div>
            </div>
          ) : artifacts.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center text-[#666]">
                <File className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No assets uploaded</p>
                <p className="mt-1 text-sm">
                  Upload files using the upload button in the chat input
                </p>
              </div>
            </div>
          ) : (
            <div className="flex flex-col gap-2">
              {artifacts.map((artifact) => (
                <AssetCard
                  key={artifact.id}
                  title={artifact.name}
                  size={formatFileSize(artifact.file_size || 0)}
                  type={artifact.mime_type || ""}
                  url={artifact.public_url || ""}
                  visibility={artifact.visibility}
                  id={artifact.id}
                  onRemove={(id) => handleDelete(id, artifact.name)}
                  onDownload={() => handleDownload(artifact)}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </>
  );

  // For mobile, use BottomSheet
  if (isMobile) {
    return (
      <BottomSheet
        trigger={<div />} // Empty trigger since we control it via props
        title={view === "info" ? "Run Details" : `Assets (${artifacts.length})`}
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) {
            onClose();
          }
        }}
        maxWidth="max-w-full"
        showDefaultFooter={false}
      >
        <div className="h-[80dvh] flex flex-col">
          {renderContent()}
        </div>

        {/* Hidden file input */}
        <input
          aria-hidden="true"
          ref={fileInputRef}
          type="file"
          accept="*/*"
          multiple
          onChange={handleFileInputChange}
          style={{ display: "none" }}
        />
      </BottomSheet>
    );
  }

  // For desktop, use original layout
  return (
    <div
      className={cn(
        "md:top-14 max-md:inset-0 h-[calc(100%-4rem)] w-screen md:w-full bg-[#0F0F10]  transform transition-transform duration-300 ease-in-out z-50 pointer-events-auto max-md:absolute",
        isOpen ? "block" : "hidden"
      )}
    >
      <div className="flex flex-col h-full">
        {renderHeader()}
        {renderContent()}
      </div>

      {/* Hidden file input */}
      <input
        aria-hidden="true"
        ref={fileInputRef}
        type="file"
        accept="*/*"
        multiple
        onChange={handleFileInputChange}
        style={{ display: "none" }}
      />
    </div>
  );
}

const InfoBar = ({ title }: { title: string }) => {
  return (
    <>
      <div className="flex items-center w-full gap-2">
        <div className="h-[1px] flex-1 bg-[#FFFFFF12]"></div>
        <span className="text-[#FFFFFF4D] text-[14px] font-berkeley uppercase tracking-[1px]">
          {title}
        </span>
        <div className="h-[1px] max-w-[30px] flex-1 bg-[#FFFFFF12]"></div>
      </div>
    </>
  );
};