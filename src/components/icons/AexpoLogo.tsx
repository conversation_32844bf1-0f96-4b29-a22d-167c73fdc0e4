import React from 'react';

interface AexpoLogoProps {
  /** Width of the logo */
  width?: number | string;
  /** Height of the logo */
  height?: number | string;
  /** Fill color for the logo */
  fill?: string;
  /** Fill opacity */
  fillOpacity?: number;
  /** CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
  /** Click handler */
  onClick?: (event: React.MouseEvent<SVGSVGElement>) => void;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the logo is focusable */
  focusable?: boolean;
  /** Custom viewBox */
  viewBox?: string;
}

const AexpoLogo: React.FC<AexpoLogoProps> = ({
  width = 71,
  height = 20,
  fill = 'white',
  fillOpacity = 0.6,
  className,
  style,
  onClick,
  'aria-label': ariaLabel = 'Aexpo logo',
  focusable = false,
  viewBox = '0 0 71 20',
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox={viewBox}
      fill="none"
      className={className}
      style={style}
      onClick={onClick}
      aria-label={ariaLabel}
      focusable={focusable}
      {...props}
    >
      <g clipPath="url(#clip0_aexpo_logo)">
        <path
          d="M9.25792 6.342C9.41592 6.112 9.58892 6.082 9.72992 6.082C9.86992 6.082 10.1039 6.112 10.2619 6.342C12.3219 9.148 16.5939 16.55 16.9889 16.953C17.5739 17.55 18.3769 17.178 18.8429 16.501C19.3029 15.834 19.4299 15.366 19.4299 14.867C19.4299 14.527 12.7769 2.253 12.1059 1.231C11.4619 0.248 11.2519 0 10.1499 0H9.32492C8.22492 0 8.06592 0.248 7.42192 1.23C6.74992 2.254 0.0969238 14.528 0.0969238 14.868C0.0969238 15.368 0.223924 15.835 0.683924 16.502C1.14992 17.179 1.95292 17.552 2.53792 16.954C2.93292 16.551 7.19892 9.149 9.25792 6.344V6.342ZM24.1989 1.105V16.449H33.5489V13.336H27.4239V10.092H32.8739V6.98H27.4239V4.218H33.5489V1.105H24.1989ZM46.2499 16.449L42.3699 10.881L45.9889 5.686H42.3269L40.5399 8.23L38.7749 5.687H35.0689L38.6869 10.904L34.8299 16.45H38.4909L40.5179 13.535L42.5449 16.45H46.2499V16.449ZM53.8219 5.467C52.3399 5.467 51.1849 6.081 50.4439 7.199V5.686H47.3699V20H50.4429V14.937C51.1829 16.054 52.3389 16.668 53.8209 16.668C56.5889 16.668 58.7909 14.148 58.7909 11.057C58.7909 7.966 56.5899 5.467 53.8219 5.467ZM53.1249 13.709C51.6209 13.709 50.4439 12.569 50.4439 11.057C50.4439 9.567 51.6209 8.404 53.1239 8.404C54.6069 8.404 55.8049 9.588 55.8049 11.057C55.8049 12.547 54.6069 13.709 53.1249 13.709ZM65.3129 5.467C62.1529 5.467 59.7549 7.878 59.7549 11.079C59.7549 14.279 62.1519 16.669 65.3119 16.669C68.4509 16.669 70.8699 14.279 70.8699 11.079C70.8699 7.879 68.4499 5.467 65.3119 5.467H65.3129ZM65.3129 8.427C66.7509 8.427 67.8629 9.544 67.8629 11.079C67.8629 12.569 66.7509 13.709 65.3129 13.709C63.8529 13.709 62.7629 12.569 62.7629 11.079C62.7629 9.544 63.8529 8.426 65.3129 8.426V8.427Z"
          fill={fill}
          fillOpacity={fillOpacity}
        />
      </g>
      <defs>
        <clipPath id="clip0_aexpo_logo">
          <rect width="71" height="20" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default AexpoLogo;