import React from 'react';

interface ExternalLinkIconProps {
  /**
   * Width of the icon
   * @default 20
   */
  width?: number | string;
  
  /**
   * Height of the icon
   * @default 20
   */
  height?: number | string;
  
  /**
   * Fill color of the icon
   * @default "white"
   */
  fill?: string;
  
  /**
   * Fill opacity of the icon
   * @default 0.8
   */
  fillOpacity?: number;
  
  /**
   * Additional CSS class name
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  
  /**
   * Additional SVG props
   */
  [key: string]: any;
}

const ExternalLinkIcon: React.FC<ExternalLinkIconProps> = ({
  width = 20,
  height = 20,
  fill = "white",
  fillOpacity = 0.8,
  className,
  style,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      className={className}
      style={style}
      {...props}
    >
      <path
        d="M5.16634 4.615C5.37713 3.9978 5.77576 3.46201 6.30634 3.08274C6.83693 2.70346 7.47289 2.49969 8.12509 2.5H14.3751C15.2039 2.5 15.9987 2.82924 16.5848 3.41529C17.1709 4.00134 17.5001 4.7962 17.5001 5.625V11.875C17.5001 13.2475 16.6151 14.4125 15.3851 14.8337V15.2887C15.3851 15.7075 15.2388 16.2425 14.8951 16.6838C14.5338 17.1488 13.9601 17.5 13.1738 17.5H5.96134C5.50582 17.5032 5.05421 17.4157 4.63276 17.2428C4.21131 17.0699 3.82844 16.815 3.50638 16.4928C3.18433 16.1707 2.92952 15.7877 2.75677 15.3662C2.58402 14.9447 2.49677 14.493 2.50009 14.0375V6.82875C2.50009 6.1425 2.74634 5.57125 3.18259 5.175C3.60759 4.78875 4.16384 4.61625 4.71134 4.61625L5.16634 4.615ZM5.00009 5.865H4.71259C4.46122 5.85373 4.21483 5.93765 4.02259 6.1C3.87509 6.23375 3.75009 6.455 3.75009 6.8275V14.0387C3.75009 15.28 4.72009 16.2513 5.96259 16.2513H13.1738C13.5476 16.2513 13.7663 16.1 13.9088 15.9163C14.0455 15.7346 14.124 15.5158 14.1338 15.2887V15H8.12509C7.29629 15 6.50143 14.6708 5.91538 14.0847C5.32933 13.4987 5.00009 12.7038 5.00009 11.875V5.865ZM10.0001 7.5H11.6163L8.30759 10.8075C8.24948 10.8656 8.20339 10.9346 8.17194 11.0105C8.14049 11.0864 8.1243 11.1678 8.1243 11.25C8.1243 11.3322 8.14049 11.4136 8.17194 11.4895C8.20339 11.5654 8.24948 11.6344 8.30759 11.6925C8.3657 11.7506 8.43469 11.7967 8.51061 11.8282C8.58654 11.8596 8.66791 11.8758 8.75009 11.8758C8.83227 11.8758 8.91365 11.8596 8.98957 11.8282C9.0655 11.7967 9.13448 11.7506 9.19259 11.6925L12.5001 8.38375V10C12.5001 10.1658 12.5659 10.3247 12.6831 10.4419C12.8004 10.5592 12.9593 10.625 13.1251 10.625C13.2909 10.625 13.4498 10.5592 13.567 10.4419C13.6842 10.3247 13.7501 10.1658 13.7501 10V6.875C13.7501 6.70924 13.6842 6.55027 13.567 6.43306C13.4498 6.31585 13.2909 6.25 13.1251 6.25H10.0001C9.83433 6.25 9.67536 6.31585 9.55815 6.43306C9.44094 6.55027 9.37509 6.70924 9.37509 6.875C9.37509 7.04076 9.44094 7.19973 9.55815 7.31694C9.67536 7.43415 9.83433 7.5 10.0001 7.5Z"
        fill={fill}
        fillOpacity={fillOpacity}
      />
    </svg>
  );
};

export default ExternalLinkIcon;