import React from 'react';

interface ShareIconProps {
  /**
   * Width of the icon
   * @default 20
   */
  width?: number | string;
  
  /**
   * Height of the icon
   * @default 20
   */
  height?: number | string;
  
  /**
   * Fill color of the icon
   * @default "#DCDCE6"
   */
  fill?: string;
  
  /**
   * Additional CSS class name
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  
  /**
   * Additional SVG props
   */
  [key: string]: any;
}

const ShareIcon: React.FC<ShareIconProps> = ({
  width = 20,
  height = 20,
  fill = "#DCDCE6",
  className,
  style,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      className={className}
      style={style}
      {...props}
    >
      <path
        d="M2.47217 14.7583V10.053C2.47217 9.53319 2.8932 9.11106 3.41301 9.11106C3.93283 9.11106 4.35386 9.53319 4.35386 10.053V14.7583C4.35386 15.008 4.45309 15.248 4.62961 15.4245C4.80612 15.6011 5.04618 15.7003 5.29581 15.7003H14.7076C14.957 15.7003 15.1962 15.6008 15.3727 15.4245C15.5492 15.248 15.6484 15.008 15.6484 14.7583V10.053C15.6484 9.53319 16.0706 9.11106 16.5904 9.11106C17.11 9.11126 17.5312 9.53332 17.5312 10.053V14.7583C17.5312 15.5072 17.2335 16.2263 16.704 16.7558C16.1745 17.2852 15.4563 17.582 14.7076 17.582H5.29581C4.54693 17.582 3.82784 17.2854 3.2983 16.7558C2.76877 16.2263 2.47217 15.5072 2.47217 14.7583ZM9.0603 11.4648V4.79508L7.8437 6.01168L7.77201 6.07675C7.40233 6.3783 6.85701 6.35628 6.5124 6.01168C6.14508 5.64416 6.14508 5.049 6.5124 4.68148L9.33604 1.85783L9.40774 1.79276C9.77742 1.49139 10.3228 1.5133 10.6673 1.85783L13.491 4.68148C13.8581 5.04895 13.8581 5.6442 13.491 6.01168C13.1234 6.37924 12.5273 6.37924 12.1597 6.01168L10.9431 4.79508V11.4648C10.9429 11.9845 10.5208 12.4057 10.0011 12.4057C9.48162 12.4055 9.06049 11.9844 9.0603 11.4648Z"
        fill={fill}
      />
    </svg>
  );
};

export default ShareIcon;