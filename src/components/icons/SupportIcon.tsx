import React from 'react';

interface SupportIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const SupportIcon: React.FC<SupportIconProps> = ({
  size = 24,
  width,
  height,
  color = '#4D4D4D',
  fill,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        d="M18.72 14.7602C19.07 13.9102 19.26 13.0002 19.26 12.0002C19.26 11.2802 19.15 10.5902 18.96 9.95019C18.31 10.1002 17.63 10.1802 16.92 10.1802C15.466 10.1818 14.0329 9.83371 12.7415 9.16541C11.4502 8.49711 10.3384 7.52815 9.5 6.34019C8.60396 8.51093 6.91172 10.2568 4.77 11.2202C4.73 11.4702 4.73 11.7402 4.73 12.0002C4.73 12.9549 4.91804 13.9003 5.2834 14.7823C5.64875 15.6643 6.18425 16.4658 6.85933 17.1409C8.22272 18.5042 10.0719 19.2702 12 19.2702C13.05 19.2702 14.06 19.0402 14.97 18.6302C15.54 19.7202 15.8 20.2602 15.78 20.2602C14.14 20.8102 12.87 21.0802 12 21.0802C9.58 21.0802 7.27 20.1302 5.57 18.4202C4.53505 17.3901 3.76627 16.1237 3.33 14.7302H2V10.1802H3.09C3.42024 8.5727 4.17949 7.0846 5.28719 5.87378C6.39489 4.66296 7.80971 3.7746 9.38153 3.30295C10.9534 2.8313 12.6235 2.79396 14.2149 3.1949C15.8062 3.59583 17.2593 4.42008 18.42 5.58019C19.6798 6.83577 20.5393 8.43648 20.89 10.1802H22V14.7302H21.94L18.38 18.0002L13.08 17.4002V15.7302H17.91L18.72 14.7602ZM9.27 11.7702C9.57 11.7702 9.86 11.8902 10.07 12.1102C10.281 12.323 10.3995 12.6105 10.3995 12.9102C10.3995 13.2099 10.281 13.4974 10.07 13.7102C9.86 13.9202 9.57 14.0402 9.27 14.0402C8.64 14.0402 8.13 13.5402 8.13 12.9102C8.13 12.2802 8.64 11.7702 9.27 11.7702ZM14.72 11.7702C15.35 11.7702 15.85 12.2802 15.85 12.9102C15.85 13.5402 15.35 14.0402 14.72 14.0402C14.09 14.0402 13.58 13.5402 13.58 12.9102C13.58 12.6078 13.7001 12.3179 13.9139 12.1041C14.1277 11.8903 14.4177 11.7702 14.72 11.7702Z"
        fill={iconFill}
      />
    </svg>
  );
};

export default SupportIcon;
