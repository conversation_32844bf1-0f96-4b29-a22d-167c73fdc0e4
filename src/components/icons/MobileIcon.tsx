import React from 'react';

interface MobileIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const MobileIcon: React.FC<MobileIconProps> = ({
  size,
  width = 21,
  height = 20,
  color = 'black',
  fill,
  className = "",
  style,
  onClick,
  ...props
}) => {
  const iconWidth = size || width;
  const iconHeight = size || height;
  const iconColor = fill || color;

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 21 20"
      fill="none"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        d="M5.5 18C4.94772 18 4.5 17.5523 4.5 17L4.5 16L16.5 16L16.5 17C16.5 17.5523 16.0523 18 15.5 18L5.5 18Z"
        fill={iconColor}
        fillOpacity="0.6"
      />
      <path
        d="M4.5 18L4.5 16L4.5 18ZM16.5 16L16.5 18L16.5 16ZM16.5 18L4.5 18L16.5 18ZM4.5 15.25L16.5 15.25L16.5 16.75L4.5 16.75L4.5 15.25Z"
        fill={iconColor}
      />
      <rect
        x="4.5"
        y="18"
        width="16"
        height="12"
        rx="2"
        transform="rotate(-90 4.5 18)"
        stroke={iconColor}
        strokeWidth="1.5"
      />
    </svg>
  );
};

export default MobileIcon;