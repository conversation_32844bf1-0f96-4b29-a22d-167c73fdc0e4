import React from 'react';

interface MobileThinProps {
  size?: number | string;
  color?: string;
  className?: string;
}

const MobileThin: React.FC<MobileThinProps> = ({
  size = 24,
  color = 'currentColor',
  className = ''
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      className={className}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.3998 4.0001C6.3998 3.78792 6.48409 3.58444 6.63412 3.43441C6.78415 3.28438 6.98763 3.2001 7.1998 3.2001H16.7998C17.012 3.2001 17.2155 3.28438 17.3655 3.43441C17.5155 3.58444 17.5998 3.78792 17.5998 4.0001V20.0001C17.5998 20.2123 17.5155 20.4158 17.3655 20.5658C17.2155 20.7158 17.012 20.8001 16.7998 20.8001H7.1998C6.98763 20.8001 6.78415 20.7158 6.63412 20.5658C6.48409 20.4158 6.3998 20.2123 6.3998 20.0001V4.0001ZM7.1998 1.6001C6.56329 1.6001 5.95284 1.85295 5.50275 2.30304C5.05266 2.75313 4.7998 3.36358 4.7998 4.0001V20.0001C4.7998 20.6366 5.05266 21.2471 5.50275 21.6972C5.95284 22.1472 6.56329 22.4001 7.1998 22.4001H16.7998C17.4363 22.4001 18.0468 22.1472 18.4969 21.6972C18.9469 21.2471 19.1998 20.6366 19.1998 20.0001V4.0001C19.1998 3.36358 18.9469 2.75313 18.4969 2.30304C18.0468 1.85295 17.4363 1.6001 16.7998 1.6001H7.1998ZM9.5998 18.6401C9.45128 18.6401 9.30884 18.6991 9.20382 18.8041C9.0988 18.9091 9.0398 19.0516 9.0398 19.2001C9.0398 19.3486 9.0988 19.4911 9.20382 19.5961C9.30884 19.7011 9.45128 19.7601 9.5998 19.7601H14.3998C14.5483 19.7601 14.6908 19.7011 14.7958 19.5961C14.9008 19.4911 14.9598 19.3486 14.9598 19.2001C14.9598 19.0516 14.9008 18.9091 14.7958 18.8041C14.6908 18.6991 14.5483 18.6401 14.3998 18.6401H9.5998Z"
        fill={color}
      />
    </svg>
  );
};

export default MobileThin;