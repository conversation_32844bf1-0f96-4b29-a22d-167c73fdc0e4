import React from 'react';

interface StarsProps {
  size?: number;
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

const Stars: React.FC<StarsProps> = ({
  size = 24,
  width,
  height,
  color = "#224BC6",
  className
}) => {
  const finalWidth = width || size;
  const finalHeight = height || size;

  return (
    <svg
      width={finalWidth}
      height={finalHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M10.8213 5.93167C10.9705 5.48892 11.6047 5.48889 11.7539 5.93167L12.6582 8.61721C13.1079 9.95302 14.1686 11.002 15.5205 11.4463L18.2383 12.3389C18.6866 12.4862 18.6866 13.1134 18.2383 13.2608L15.5205 14.1543C14.1686 14.5986 13.1079 15.6466 12.6582 16.9824L11.7539 19.668C11.6048 20.111 10.9704 20.111 10.8213 19.668L9.917 16.9824C9.46731 15.6467 8.40659 14.5986 7.0547 14.1543L4.33595 13.2608C3.88806 13.1132 3.88798 12.4863 4.33595 12.3389L7.0547 11.4463C8.40665 11.002 9.46729 9.95302 9.917 8.61721L10.8213 5.93167ZM16.8984 4.13284C16.9581 3.95574 17.2118 3.9557 17.2715 4.13284L17.6084 5.13382C17.804 5.71436 18.2649 6.17014 18.8526 6.36331L19.8652 6.69632C20.0445 6.7553 20.0446 7.00555 19.8652 7.06448L18.8526 7.39749C18.2648 7.59072 17.8039 8.04628 17.6084 8.62698L17.2715 9.62698C17.2118 9.80417 16.9581 9.80417 16.8984 9.62698L16.5615 8.62698C16.366 8.04634 15.905 7.59076 15.3174 7.39749L14.3047 7.06448C14.1254 7.00554 14.1254 6.75525 14.3047 6.69632L15.3174 6.36331C15.905 6.1701 16.3659 5.71433 16.5615 5.13382L16.8984 4.13284Z"
        fill={color}
      />
    </svg>
  );
};

export default Stars;
