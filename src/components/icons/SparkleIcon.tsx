import React from 'react';

interface SparkleIconProps {
  /**
   * Width of the icon
   * @default 20
   */
  width?: number | string;
  
  /**
   * Height of the icon
   * @default 20
   */
  height?: number | string;
  
  /**
   * Fill color of the icon
   * @default "white"
   */
  fill?: string;
  
  /**
   * Additional CSS class name
   */
  className?: string;
  
  /**
   * Inline styles
   */
  style?: React.CSSProperties;
  
  /**
   * Additional SVG props
   */
  [key: string]: any;
}

const SparkleIcon: React.FC<SparkleIconProps> = ({
  width = 20,
  height = 20,
  fill = "white",
  className,
  style,
  ...props
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 20"
      fill="none"
      className={className}
      style={style}
      {...props}
    >
      <path
        d="M5.66504 11.9571C6.06881 11.9574 6.39941 12.2879 6.39941 12.6915C6.3992 13.0949 6.06867 13.4247 5.66504 13.4249H4.59277C5.19315 14.3275 6.00712 15.068 6.96289 15.5802C7.91877 16.0924 8.98667 16.3612 10.0713 16.3614C12.7448 16.3614 15.044 14.7683 16.0869 12.4855C16.2265 12.17 16.5788 12.0157 16.9092 12.089C17.015 12.1142 17.1141 12.1629 17.1992 12.2306C17.2842 12.2982 17.354 12.3833 17.4023 12.4806C17.4506 12.5778 17.477 12.6846 17.4795 12.7931C17.482 12.9017 17.4599 13.01 17.416 13.1095C16.1454 15.8914 13.3397 17.8292 10.0713 17.8292C7.33177 17.8292 4.92251 16.4636 3.46094 14.379V15.6271C3.46094 16.0308 3.13052 16.3614 2.72656 16.3614C2.32268 16.3613 1.99219 16.0307 1.99219 15.6271V12.6915C1.99219 12.2879 2.32268 11.9572 2.72656 11.9571H5.66504ZM10.0713 6.6007C10.1417 6.60074 10.2103 6.62124 10.2695 6.65929C10.3288 6.69738 10.3761 6.75149 10.4053 6.81554L11.2129 8.60656L13.0049 9.42101C13.069 9.4502 13.124 9.49753 13.1621 9.55675C13.2001 9.61588 13.2207 9.68472 13.2207 9.75499C13.2207 9.82524 13.2001 9.89411 13.1621 9.95324C13.124 10.0125 13.069 10.0598 13.0049 10.089L11.2129 10.8966L10.3975 12.6876C10.3683 12.7515 10.3217 12.8058 10.2627 12.8439C10.2034 12.882 10.1339 12.9025 10.0635 12.9025C9.99313 12.9024 9.92441 12.8819 9.86523 12.8439C9.80601 12.8058 9.75871 12.7516 9.72949 12.6876L8.92188 10.9034L7.12988 10.089C6.84344 9.95685 6.84344 9.5463 7.12988 9.41417L8.92188 8.60656L9.7373 6.81554C9.76645 6.75162 9.81301 6.69737 9.87207 6.65929C9.93132 6.6212 10.0008 6.6007 10.0713 6.6007ZM3.87891 4.98058C3.98712 4.97005 4.09691 4.98385 4.19922 5.02062C4.30136 5.0574 4.39409 5.11659 4.4707 5.19347C4.7056 5.4357 4.75681 5.81695 4.57324 6.09581C4.10325 6.80037 3.75782 7.60043 3.58887 8.4591C3.52277 8.78938 3.21479 9.01766 2.87695 9.01769C2.76803 9.01771 2.65994 8.99305 2.56152 8.9464C2.46336 8.89979 2.37639 8.83227 2.30762 8.74816C2.23874 8.66387 2.19014 8.56475 2.16406 8.4591C2.138 8.35347 2.13549 8.24342 2.15723 8.13683C2.36107 7.12385 2.76303 6.16035 3.33984 5.30285C3.40022 5.21268 3.48009 5.13689 3.57324 5.08117C3.66643 5.02546 3.77083 4.99112 3.87891 4.98058ZM15.6641 5.19347C15.9871 4.86328 16.5377 4.92151 16.7949 5.30285C17.3605 6.15431 17.7645 7.10921 17.9775 8.13683C17.9993 8.24342 17.9968 8.35347 17.9707 8.4591C17.9446 8.56475 17.896 8.66387 17.8271 8.74816C17.7583 8.8324 17.6706 8.89978 17.5723 8.9464C17.4739 8.99295 17.3666 9.01771 17.2578 9.01769C16.92 9.01767 16.611 8.78939 16.5449 8.4591C16.3686 7.6005 16.0315 6.80032 15.5615 6.09581C15.3706 5.8096 15.4218 5.4357 15.6641 5.19347ZM10.8018 2.56554C10.8018 2.09577 11.2282 1.7509 11.6836 1.83898C12.7117 2.05187 13.674 2.4555 14.5186 3.02062C14.9076 3.2776 14.9589 3.82808 14.6211 4.15831C14.3787 4.4005 14.0042 4.45168 13.7178 4.26085C13.0201 3.79114 12.2196 3.4463 11.3604 3.27745C11.0299 3.2114 10.8018 2.90313 10.8018 2.56554ZM8.45117 1.83898C8.90654 1.7509 9.33301 2.09577 9.33301 2.56554C9.33294 2.90304 9.10523 3.2108 8.76758 3.26964C7.9083 3.4458 7.11521 3.78429 6.41016 4.25402C6.12371 4.44486 5.74823 4.39273 5.50586 4.1505C5.18321 3.82755 5.23466 3.27749 5.61621 3.02062C6.46074 2.4555 7.42306 2.05187 8.45117 1.83898Z"
        fill={fill}
      />
    </svg>
  );
};

export default SparkleIcon;