import React from 'react';

interface TwoXIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
  showShadow?: boolean;
}

const TwoXIcon: React.FC<TwoXIconProps> = ({
  size,
  width = 98,
  height = 151,
  color = 'white',
  fill,
  className,
  style,
  onClick,
  showShadow = true,
  ...props
}) => {
  const iconWidth = size || width;
  const iconHeight = size || height;
  const iconFill = fill || color;

  // Generate unique filter ID to avoid conflicts when multiple instances are used
  const filterId = React.useMemo(() => `filter_2x_${Math.random().toString(36).substring(2, 11)}`, []);

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 98 151"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <g filter={showShadow ? `url(#${filterId})` : undefined}>
        <path
          d="M37.2297 22.8963V20.1704L42.9496 15.1626C43.3784 14.7746 43.7434 14.4198 44.0446 14.0982C44.3458 13.7715 44.5755 13.4448 44.7338 13.1181C44.892 12.7863 44.9711 12.4264 44.9711 12.0384C44.9711 11.6045 44.8767 11.2344 44.6878 10.9281C44.4989 10.6167 44.2386 10.3768 43.9068 10.2084C43.575 10.0399 43.1947 9.95567 42.7659 9.95567C42.332 9.95567 41.9516 10.045 41.6249 10.2237C41.2982 10.3972 41.043 10.6499 40.8592 10.9817C40.6806 11.3135 40.5912 11.7168 40.5912 12.1916H37C37 11.1247 37.2399 10.2033 37.7198 9.42733C38.1996 8.6514 38.8735 8.05414 39.7413 7.63555C40.6142 7.21185 41.6275 7 42.7812 7C43.9706 7 45.0043 7.19909 45.8823 7.59726C46.7604 7.99543 47.4393 8.55186 47.9191 9.26653C48.4041 9.97609 48.6466 10.8005 48.6466 11.7398C48.6466 12.3371 48.5266 12.9292 48.2867 13.5163C48.0468 14.1033 47.6154 14.7516 46.9926 15.4612C46.3749 16.1708 45.4969 17.0207 44.3586 18.011L42.4825 19.7415V19.8411H48.838V22.8963H37.2297Z"
          fill={iconFill}
        />
        <path
          d="M53.6491 11.87L55.4653 15.5023L57.3461 11.87H60.8851L57.8055 17.3831L61 22.8963H57.4897L55.4653 19.2352L53.4912 22.8963H49.9306L53.1322 17.3831L50.0885 11.87H53.6491Z"
          fill={iconFill}
        />
      </g>
      {showShadow && (
        <defs>
          <filter id={filterId} x="0" y="0" width="98" height="150.896" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
            <feFlood floodOpacity="0" result="BackgroundImageFix"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="6"/>
            <feGaussianBlur stdDeviation="6.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0"/>
            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="23"/>
            <feGaussianBlur stdDeviation="11.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
            <feBlend mode="normal" in2="effect1_dropShadow" result="effect2_dropShadow"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="51"/>
            <feGaussianBlur stdDeviation="15.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0"/>
            <feBlend mode="normal" in2="effect2_dropShadow" result="effect3_dropShadow"/>
            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
            <feOffset dy="91"/>
            <feGaussianBlur stdDeviation="18.5"/>
            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0"/>
            <feBlend mode="normal" in2="effect3_dropShadow" result="effect4_dropShadow"/>
            <feBlend mode="normal" in="SourceGraphic" in2="effect4_dropShadow" result="shape"/>
          </filter>
        </defs>
      )}
    </svg>
  );
};

export default TwoXIcon;
