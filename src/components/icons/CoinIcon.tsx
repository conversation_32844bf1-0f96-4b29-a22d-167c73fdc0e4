import React from 'react';

interface CoinIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const CoinIcon: React.FC<CoinIconProps> = ({
  size = 24,
  width,
  height,
  color = '#4D4D4D',
  fill,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        d="M12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2ZM12.5107 7.7373C12.1812 6.92076 11.9035 6.64719 11.5078 7.62207C10.6422 9.69969 9.60868 10.6895 7.57422 11.5469C7.36996 11.6412 7.00239 11.8057 7 12C7.00264 12.1942 7.36761 12.3578 7.57422 12.4521C9.60626 13.3071 10.6422 14.3002 11.5078 16.3779C11.9082 17.37 12.1869 17.0582 12.5107 16.2627C13.3788 14.2545 14.3482 13.3223 16.3955 12.4521C16.61 12.3499 16.9637 12.2143 17 12.0107V11.9873C16.9641 11.785 16.6125 11.6473 16.3955 11.5449V11.5469C14.3485 10.6768 13.3787 9.74605 12.5107 7.7373Z"
        fill={iconFill}
      />
    </svg>
  );
};

export default CoinIcon;
