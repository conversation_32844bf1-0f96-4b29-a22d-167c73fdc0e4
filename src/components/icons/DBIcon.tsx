import React from 'react';

interface DBIconProps {
  size?: number | string;
  width?: number | string;
  height?: number | string;
  color?: string;
  fill?: string;
  className?: string;
  style?: React.CSSProperties;
  onClick?: () => void;
}

const DBIcon: React.FC<DBIconProps> = ({
  size = 24,
  width,
  height,
  color = '#4D4D4D',
  fill,
  className,
  style,
  onClick,
  ...props
}) => {
  const iconWidth = width || size;
  const iconHeight = height || size;
  const iconFill = fill || color;

  return (
    <svg
      width={iconWidth}
      height={iconHeight}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      style={style}
      onClick={onClick}
      {...props}
    >
      <path
        d="M20.1846 13.5137C20.1662 14.4446 20.1332 15.2279 20.0996 15.833C20.0751 16.2761 20.0434 16.719 20.0059 17.1611L20.0039 17.1826L20 17.2314C19.9903 17.8612 19.6891 18.4137 19.248 18.8652C18.8017 19.3212 18.1851 19.7039 17.4707 20.0127C16.0399 20.6315 14.1052 21 12 21C9.89486 21 7.96008 20.632 6.5293 20.0137C5.8149 19.7045 5.19835 19.3216 4.75195 18.8652C4.31086 18.4141 4.0096 17.861 4 17.2314L3.99316 17.1611L3.9707 16.8848C3.94378 16.5345 3.91987 16.1838 3.90039 15.833C3.85827 15.0605 3.83036 14.2872 3.81543 13.5137C4.29536 13.9275 4.88626 14.2829 5.5293 14.5752C7.21607 15.3419 9.50412 15.7998 12 15.7998C14.4959 15.7998 16.7839 15.3419 18.4707 14.5752C19.1137 14.2829 19.7046 13.9271 20.1846 13.5137ZM9.2002 17.2998C8.98815 17.2998 8.78477 17.3843 8.63477 17.5342C8.48482 17.6841 8.40048 17.8876 8.40039 18.0996C8.40039 18.3118 8.48474 18.516 8.63477 18.666C8.78476 18.8158 8.98821 18.9004 9.2002 18.9004C9.4122 18.9003 9.61566 18.8159 9.76562 18.666C9.91565 18.516 10 18.3118 10 18.0996C9.99991 17.8876 9.91557 17.6841 9.76562 17.5342C9.61565 17.3842 9.41226 17.2999 9.2002 17.2998ZM6.59961 16.5996C6.38762 16.5997 6.1841 16.6841 6.03418 16.834C5.88415 16.984 5.7998 17.1882 5.7998 17.4004C5.79992 17.6124 5.88425 17.8159 6.03418 17.9658C6.18411 18.1157 6.38759 18.2001 6.59961 18.2002C6.81178 18.2002 7.01599 18.1158 7.16602 17.9658C7.3159 17.8159 7.40028 17.6124 7.40039 17.4004C7.40039 17.1882 7.31604 16.984 7.16602 16.834C7.01599 16.684 6.81178 16.5996 6.59961 16.5996ZM4 5.59961C4 6.24218 4.30382 6.80594 4.75195 7.26465C5.19835 7.72065 5.8149 8.10429 6.5293 8.41309C7.96009 9.03187 9.89443 9.40039 12 9.40039C14.1056 9.40039 16.0399 9.03148 17.4707 8.41309C18.1851 8.1039 18.8011 7.72102 19.2471 7.26465C19.6957 6.80589 20 6.22667 20 5.58398L20.0303 6.11816C20.0491 6.47015 20.0744 6.97252 20.0996 7.5752C20.1424 8.60158 20.1847 9.92137 20.1963 11.2861C20.145 11.9916 19.465 12.804 17.9746 13.4814C16.4762 14.163 14.364 14.5996 12 14.5996C9.63601 14.5996 7.52378 14.1632 6.02539 13.4824C4.5347 12.8044 3.85491 11.9913 3.80371 11.2861C3.81571 9.92103 3.85759 8.60153 3.90039 7.5752C3.92794 6.91655 3.96097 6.25773 3.99902 5.59961L4 5.58203V5.59961ZM9.2002 11.0996C8.98819 11.0996 8.78476 11.1842 8.63477 11.334C8.48474 11.484 8.40039 11.6882 8.40039 11.9004C8.40052 12.1124 8.48485 12.3159 8.63477 12.4658C8.78476 12.6156 8.98818 12.7002 9.2002 12.7002C9.41223 12.7001 9.61565 12.6157 9.76562 12.4658C9.91554 12.3159 9.99987 12.1124 10 11.9004C10 11.6882 9.91565 11.484 9.76562 11.334C9.61566 11.1841 9.41223 11.0997 9.2002 11.0996ZM6.59961 10.4004C6.38765 10.4005 6.1841 10.4839 6.03418 10.6338C5.88415 10.7838 5.7998 10.988 5.7998 11.2002C5.79988 11.4122 5.88426 11.6157 6.03418 11.7656C6.18411 11.9156 6.38758 11.9999 6.59961 12C6.81178 12 7.01599 11.9157 7.16602 11.7656C7.31589 11.6157 7.40032 11.4122 7.40039 11.2002C7.40039 10.988 7.31604 10.7838 7.16602 10.6338C7.01599 10.4838 6.81175 10.4004 6.59961 10.4004ZM12 3C13.9814 3 15.7466 3.34777 16.9941 3.8877C17.6189 4.15769 18.0869 4.46482 18.3896 4.77441C18.9037 5.3003 18.9038 5.8999 18.3896 6.42578C18.0864 6.73538 17.6189 7.04152 16.9941 7.31152C15.7465 7.85111 13.9816 8.20019 12 8.2002C10.0184 8.2002 8.25345 7.85151 7.00586 7.31152C6.38106 7.04152 5.91315 6.73538 5.61035 6.42578C5.09617 5.8999 5.09625 5.3003 5.61035 4.77441C5.91355 4.46482 6.38106 4.15769 7.00586 3.8877C8.25344 3.34818 10.0186 3 12 3Z"
        fill={iconFill}
      />
    </svg>
  );
};

export default DBIcon;
