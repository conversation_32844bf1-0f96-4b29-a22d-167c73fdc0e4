const CommentWithStar = ({
  size = 24,
  color = "#4D4D4D",
  className = "",
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
      {...props}
    >
      <path
        d="M8.92217 10.6793C8.92217 10.404 9.14532 10.0213 9.42058 10.0213H15.4016C15.6769 10.0213 15.9 10.404 15.9 10.6793C15.9 10.9546 15.6769 11.3373 15.4016 11.3373H9.42058C9.14532 11.3373 8.92217 10.9546 8.92217 10.6793Z"
        fill={color}
      />
      <path
        d="M8.92217 13.6703C8.92217 13.395 9.14532 13.0123 9.42058 13.0123H14.4048C14.68 13.0123 14.9032 13.395 14.9032 13.6703C14.9032 13.9456 14.68 14.3283 14.4048 14.3283H9.42058C9.14532 14.3283 8.92217 13.9456 8.92217 13.6703Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.9158 6.93867C10.9158 6.74035 10.9946 6.55016 11.1348 6.40993C11.275 6.2697 11.4652 6.19092 11.6635 6.19092H16.8969C17.69 6.19092 18.4506 6.50604 19.0115 7.06696C19.5723 7.62788 19.8874 8.38865 19.8874 9.18191V15.1639C19.8874 15.9571 19.5723 16.7179 19.0115 17.2788C18.4506 17.8398 17.69 18.1549 16.8969 18.1549H7.98813L5.21394 20.9265C5.10938 21.031 4.97623 21.1021 4.83129 21.1309C4.68635 21.1597 4.53613 21.1449 4.3996 21.0883C4.26307 21.0318 4.14635 20.936 4.0642 20.8132C3.98204 20.6904 3.93812 20.5459 3.93799 20.3981L3.93799 11.9236C3.93799 11.7253 4.01676 11.5351 4.15696 11.3949C4.29717 11.2547 4.48733 11.1759 4.68561 11.1759C4.8839 11.1759 5.07406 11.2547 5.21427 11.3949C5.35447 11.5351 5.43324 11.7253 5.43324 11.9236V18.5936L7.37009 16.6594H16.8969C17.2934 16.6594 17.6737 16.5018 17.9542 16.2214C18.2346 15.9409 18.3921 15.5605 18.3921 15.1639V9.18191C18.3921 8.78528 18.2346 8.40489 17.9542 8.12443C17.6737 7.84397 17.2934 7.68641 16.8969 7.68641H11.6635C11.4652 7.68641 11.275 7.60763 11.1348 7.4674C10.9946 7.32717 10.9158 7.13698 10.9158 6.93867Z"
        fill={color}
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.24564 2.5311C4.30119 2.37566 4.40342 2.24119 4.53835 2.14611C4.67327 2.05103 4.83429 2 4.99933 2C5.16438 2 5.32539 2.05103 5.46032 2.14611C5.59524 2.24119 5.69748 2.37566 5.75303 2.5311L6.33597 4.17616C6.37578 4.28846 6.44015 4.39045 6.52438 4.4747C6.60862 4.55895 6.71059 4.62333 6.82287 4.66314L8.46899 5.24618C8.62441 5.30174 8.75885 5.40399 8.85392 5.53894C8.94898 5.67389 9 5.83493 9 6C9 6.16508 8.94898 6.32612 8.85392 6.46106C8.75885 6.59601 8.62441 6.69826 8.46899 6.75382L6.82287 7.33686C6.71059 7.37668 6.60862 7.44106 6.52438 7.52531C6.44015 7.60955 6.37578 7.71155 6.33597 7.82384L5.75303 9.4689C5.69748 9.62435 5.59524 9.75882 5.46032 9.85389C5.32539 9.94897 5.16438 10 4.99933 10C4.83429 10 4.67327 9.94897 4.53835 9.85389C4.40342 9.75882 4.30119 9.62435 4.24564 9.4689L3.6627 7.82251C3.62289 7.71021 3.55852 7.60822 3.47428 7.52397C3.39005 7.43972 3.28808 7.37534 3.1758 7.33553L1.53102 6.75382C1.37559 6.69826 1.24115 6.59601 1.14609 6.46106C1.05102 6.32612 1 6.16508 1 6C1 5.83493 1.05102 5.67389 1.14609 5.53894C1.24115 5.40399 1.37559 5.30174 1.53102 5.24618L3.17713 4.66314C3.28941 4.62333 3.39138 4.55895 3.47562 4.4747C3.55985 4.39045 3.62422 4.28846 3.66403 4.17616L4.24564 2.5311Z"
        fill={color}
      />
    </svg>
  );
};

export default CommentWithStar;
