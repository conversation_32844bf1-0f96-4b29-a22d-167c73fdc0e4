import React from 'react';

interface RefreshIconProps {
  /** Width of the icon */
  width?: number | string;
  /** Height of the icon */
  height?: number | string;
  /** Fill color for the icon */
  fill?: string;
  /** CSS class name */
  className?: string;
  /** Additional inline styles */
  style?: React.CSSProperties;
  /** Click handler */
  onClick?: (event: React.MouseEvent<SVGSVGElement>) => void;
  /** Accessibility label */
  'aria-label'?: string;
  /** Whether the icon is focusable */
  focusable?: boolean;
  /** Custom viewBox */
  viewBox?: string;
  /** Whether the icon should rotate (adds animation) */
  spinning?: boolean;
}

const RefreshIcon: React.FC<RefreshIconProps> = ({
  width = 20,
  height = 20,
  fill = '#737380',
  className,
  style,
  onClick,
  'aria-label': ariaLabel = 'Refresh icon',
  focusable = false,
  viewBox = '0 0 20 20',
  spinning = false,
  ...props
}) => {
  const combinedStyle: React.CSSProperties = {
    ...style,
    ...(spinning && {
      animation: 'spin 1s linear infinite',
      transformOrigin: 'center',
    }),
  };

  const combinedClassName = `${className || ''} ${spinning ? 'refresh-spinning' : ''}`.trim();

  return (
    <>
      {spinning && (
        <style>
          {`
            @keyframes spin {
              from { transform: rotate(0deg); }
              to { transform: rotate(360deg); }
            }
          `}
        </style>
      )}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={width}
        height={height}
        viewBox={viewBox}
        fill="none"
        className={combinedClassName}
        style={combinedStyle}
        onClick={onClick}
        aria-label={ariaLabel}
        focusable={focusable}
        {...props}
      >
        <path
          d="M10.0418 16.6667C8.18072 16.6667 6.59738 16.0209 5.29183 14.7292C3.98627 13.4375 3.3335 11.8612 3.3335 10V9.85421L2.00016 11.1875L0.833496 10.0209L4.16683 6.68754L7.50016 10.0209L6.3335 11.1875L5.00016 9.85421V10C5.00016 11.3889 5.48975 12.5695 6.46891 13.5417C7.44808 14.5139 8.63905 15 10.0418 15C10.4029 15 10.7571 14.9584 11.1043 14.875C11.4516 14.7917 11.7918 14.6667 12.1252 14.5L13.3752 15.75C12.8474 16.0556 12.3057 16.2848 11.7502 16.4375C11.1946 16.5903 10.6252 16.6667 10.0418 16.6667ZM15.8335 13.3125L12.5002 9.97921L13.6668 8.81254L15.0002 10.1459V10C15.0002 8.61115 14.5106 7.4306 13.5314 6.45837C12.5522 5.48615 11.3613 5.00004 9.9585 5.00004C9.59738 5.00004 9.24322 5.04171 8.896 5.12504C8.54877 5.20837 8.2085 5.33337 7.87516 5.50004L6.62516 4.25004C7.15294 3.94449 7.69461 3.71532 8.25016 3.56254C8.80572 3.40976 9.37516 3.33337 9.9585 3.33337C11.8196 3.33337 13.4029 3.97921 14.7085 5.27087C16.014 6.56254 16.6668 8.13893 16.6668 10V10.1459L18.0002 8.81254L19.1668 9.97921L15.8335 13.3125Z"
          fill={fill}
        />
      </svg>
    </>
  );
};

export default RefreshIcon;