import { useState } from "react";
import { createPortal } from "react-dom";
import { Loader2, X, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { usePayment } from "@/hooks/use-payment";
import { useCredits } from "@/contexts";
import { formatDateOnly } from "@/lib/utils/dateFormatter";
import { cn } from "@/lib/utils";
import { agentApi } from "@/services/agentApi";
import { useToast } from "@/hooks/use-toast";
import { SubscriptionPlan, CurrentSubscription } from "@/store/api/apiSlice";
import { useProModePayment } from "@/hooks/useProModePayment";
import CommentWithStar from "../icons/CommentWithStar";
import NormalRobot from "../icons/Robots/NormalRobot";
import Mobile from "../icons/Mobile";
import DBIcon from "../icons/DBIcon";
import CoinIcon from "../icons/CoinIcon";
import SupportIcon from "../icons/SupportIcon";
import SparkleIcon from "../icons/SparkleIcon";
import Stars from "../icons/Stars";
import { URL_LINKS } from "@/constants/constants";

const buttonStyle = {
  background: `
    linear-gradient(90deg, #ffffff30, #00000050),
    #F3CA5F
  `,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundBlendMode: 'overlay, normal',
  boxShadow: `
    0px 0px 20px 0px #F3CA5F66,
    0px 2.18px 3.64px 1.45px #FFFFFF66 inset,
    0px -1.45px 0px 0px #00000033 inset,
    0px 0.73px 0.73px 0.73px #0000001A
  `,
};

const plainButtonStyle = {
  background: `#fff`,
  backgroundSize: 'cover',
  backgroundPosition: 'center',
  backgroundBlendMode: 'overlay, normal',
};

interface ManageSubscriptionModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export function ManageSubscriptionModal({ isOpen, onOpenChange }: ManageSubscriptionModalProps) {
  const { toast } = useToast();
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);

  // Use the payment hook for subscription management
  const { isUpgradeLoading: isEditBillingLoading, handleManageSubscription } = usePayment();
  const { creditResponse, subscriptionPlansResponse } = useCredits();

  const {paidBefore30July} = useProModePayment(); 

  // Generic upgrade handler for any plan
  const handleUpgradeToSpecificPlan = async (plan: SubscriptionPlan | CurrentSubscription) => {
    setLoadingPlanId(plan.id);
    try {
      const { url } = await agentApi.createCheckoutSession(0, true, plan.id);
      if (url) {
        window.location.href = url;
      } else {
        toast({
          title: "Error",
          description: "Failed to create checkout session",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Failed to create checkout session:', error);
      toast({
        title: "Error",
        description: "Failed to create checkout session",
        variant: "destructive",
      });
    } finally {
      setLoadingPlanId(null);
    }
  };

  // Helper function to get tier display name
  const getTierDisplayName = (tierName: string): string => {
    const name = tierName.replaceAll(" ", "").toLowerCase();
    if (name.includes("standard")) return "Standard";
    if (name.includes("starter")) return "Starter";
    if (name.includes("pro")) return "Pro";
    return tierName;
  };

  // Helper function to determine if a plan is an upgrade or downgrade
  const getChangeType = (plan: SubscriptionPlan | CurrentSubscription): 'upgrade' | 'downgrade' => {
    if (!currentSubscription) return 'upgrade';
    return plan.amount > currentSubscription.amount ? 'upgrade' : 'downgrade';
  };

  // Get current subscription and all available plans
  const currentSubscription = subscriptionPlansResponse?.current_subscription;
  const allPlans = subscriptionPlansResponse?.available_plans || [];

  // Filter out Emergent Pro plans if user hasn't paid before July 30th
  const filteredPlans = allPlans.filter(plan =>
    plan.tier_name.toLowerCase() !== "emergent pro" || paidBefore30July
  );

  // Combine current subscription with available plans, ensuring current is first
  const allSubscriptionOptions = currentSubscription
    ? [currentSubscription, ...filteredPlans.filter(plan => plan.id !== currentSubscription.id)]
    : filteredPlans;


    const ProPlanFeatures = () => {
      return <div className="flex-grow space-y-2 md:space-y-4">
      <div className="flex items-start">
        <CommentWithStar color="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="font-medium text-[#CCC]">System Prompt Edit</span>
        </span>
      </div>
      <div className="flex items-start">
        <NormalRobot primaryColor="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-[#CCC]">Ability to build Custom Agents</span>
        </span>
      </div>
      <div className="flex items-start">
        <Mobile color="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-[#CCC]">Mobile App Development</span>
        </span>
      </div>
      <div className="flex items-start">
        <DBIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-[#CCC]">2x Bigger Machine</span>
        </span>
      </div>
      <div className="flex items-start">
        <CoinIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-[#CCC]">750 Monthly Credits</span>
        </span>
      </div>
      <div className="flex items-start">
        <SupportIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-[#CCC]">Priority Support</span>
        </span>
      </div>
    </div>
    }
  
  
    const StandardPlanFeatures = ({ plan } :{
      plan: any
    }) => {
      return <div className="flex-grow space-y-2 md:space-y-4">
      <div className="flex items-start">
        <CoinIcon fill="#CCC"  className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="font-medium">{plan.ecu_credits} credits</span>
          <span className="text-gray-400"> per month</span>
        </span>
      </div>
      {/* <div className="flex items-start">
        <CoinIcon fill="#CCC"  className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="font-medium">5 daily credits</span>
          <span className="text-gray-400"> (up to 25/month)</span>
        </span>
      </div> */}
      <div className="flex items-start">
        <SparkleIcon fill="#ccc" className="w-5 h-5 mr-2 mt-0.5" />
        <span className="text-base">
          <span className="text-gray-400">Ability to buy additional credits</span>
        </span>
      </div>
    </div>
    }

  if (!isOpen) return null;

  return createPortal(
    <AnimatePresence>
      <div className="fixed inset-0 z-[999] flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="absolute inset-0 bg-black/80 backdrop-blur-sm"
          onClick={() => onOpenChange(false)}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="relative  flex flex-col items-center pb-[40px] max-h-[90vh] bg-[#18181A] rounded-2xl shadow-2xl overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-start justify-between w-full gap-4 p-4 md:p-6">
            <div className="flex flex-col items-center justify-center gap-1">
              <h2 className="text-[18px] md:text-[26px] font-medium text-[#E6E6E6]">
                Manage Your Subscriptions
              </h2>
              {creditResponse?.monthly_credits_refresh_date && (
                <span className="text-[#FFFFFF]/40 text-center max-md:text-[12px] max-w-[90%] font-inter font-medium">
                  {creditResponse?.subscription.status == "initiated_cancellation" ? "Cancels on" : "Refreshes on"}
                  <span className="text-[#FFFFFF] font-inter font-medium ml-1">
                    {formatDateOnly(creditResponse?.monthly_credits_refresh_date)}
                  </span>
                </span>
              )}
            </div>
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              disabled={loadingPlanId !== null}
              className={cn(
                "p-2 bg-[#FFFFFF05]  backdrop-blur-lg hover:bg-[#242424] rounded-full transition-colors",
                loadingPlanId !== null && "opacity-50 cursor-not-allowed"
              )}
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-[#737780]" />
            </button>
          </div>

          {/* Content */}
          <div className="p-4 flex items-center justify-center md:p-8 max-h-[calc(90dvh-140px)]">
            <div className="flex gap-3 overflow-x-auto md:gap-6">
              {allSubscriptionOptions.length > 0 ? (
                allSubscriptionOptions.map((plan) => {
                  const isCurrentPlan = currentSubscription && plan.id === currentSubscription.id;

                  return (
                    <div
                      key={plan.id}
                      className={cn(
                        "bg-[#1C1C1F] rounded-xl p-3 md:p-6 flex flex-col relative transition-all ease-in-out min-h-[350px] duration-200 md:min-w-[350px] flex-shrink-0",
                        allSubscriptionOptions.length === 1 ? "w-full" : "w-fit",
                      )}
                    >
                      <h3 className={cn(
                        "text-base gap-1 mb-2 text-[#808080] font-semibold flex items-center font-['Inter']",
                        isCurrentPlan && getTierDisplayName(plan.tier_name) === 'Pro' && 'text-[#F3CA5F]'
                      )}>
                        {getTierDisplayName(plan.tier_name)} {getTierDisplayName(plan.tier_name) === 'Pro' && <Stars color="#F3CA5F" size={24} height={24} width={24} />}
                      </h3>
                      <div className="flex items-baseline mb-6">
                        <span className="text-[24px] font-bold md:text-4xl">${plan.amount}</span>
                        <span className="ml-2 text-gray-400">/ month</span>
                      </div>

                      {isCurrentPlan ? (
                        <button
                          type="button"
                          style={getTierDisplayName(plan.tier_name) === 'Pro' ? buttonStyle : plainButtonStyle}
                          className="w-full mb-4 cursor-auto md:mb-6 p-[10px] pr-[16px] text-base font-semibold rounded-full items-center flex justify-center bg-[#FFFFFF] text-[#0F0F10] border-[#3A3A3B]"
                        >
                          Current Active Plan
                        </button>
                      ) : (
                        <button
                          type="button"
                          style={getTierDisplayName(plan.tier_name) === 'Pro' ? buttonStyle : plainButtonStyle}
                          className="w-full mb-4 md:mb-6 p-[10px] pr-[16px] rounded-full font-brockmann font-semibold text-base text-black tracking-[-0.2px]"
                          onClick={() => handleUpgradeToSpecificPlan(plan)}
                          disabled={loadingPlanId === plan.id}
                        >
                          {loadingPlanId === plan.id ? (
                            <div className="flex items-center">
                              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                              <span>Processing...</span>
                            </div>
                          ) : (
                            `${getChangeType(plan) === 'upgrade' ? 'Upgrade' : 'Downgrade'} to ${getTierDisplayName(plan.tier_name)}`
                          )}
                        </button>
                      )}

                      {getTierDisplayName(plan.tier_name) === 'Pro' && <ProPlanFeatures />}
                      {getTierDisplayName(plan.tier_name) === 'Standard' && <StandardPlanFeatures plan={plan} />}
                      {getTierDisplayName(plan.tier_name) === 'Starter' && <StandardPlanFeatures plan={plan} />}
                    </div>
                  );
                })
              ) : (
                <div className="col-span-full bg-[#262629] rounded-xl p-6 flex flex-col items-center justify-center border transition-colors duration-200">
                  <div className="text-center">
                    <h3 className="text-lg font-medium mb-2 text-[#E6E6E6]">No Subscription Plans Available</h3>
                    <p className="text-gray-400">
                      No subscription plans are currently available for your account.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer with support link and manage subscription */}
          <div className="flex items-center justify-between w-full px-8 md:px-8">
            <span className="text-[#FFFFFF66] font-inter"><a href={URL_LINKS.support} target="_blank" rel="noopener noreferrer" className="text-[#FFFFFF] font-inter">Contact support</a></span>
            {currentSubscription && (
              <div
                className={cn(`flex items-center cursor-pointer group ${isEditBillingLoading ? 'pointer-events-none opacity-70' : ''} `)}
                onClick={handleManageSubscription}
              >
                {isEditBillingLoading ? (
                  <div className="flex items-center text-white/50 text-[16px] font-['Inter']">
                    <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  <>
                    <span className="text-white/50 group-hover:text-white text-[16px] font-['Inter'] flex items-center">
                      {creditResponse?.subscription.status == "initiated_cancellation" ? "Resume" : "Cancel"} Subscription
                    </span>
                    <ChevronDown className="w-4 h-4 ml-1 -rotate-90 opacity-50 group-hover:opacity-100" />
                  </>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>,
    document.body
  );
}
