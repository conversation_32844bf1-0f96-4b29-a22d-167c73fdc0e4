import {
  <PERSON>,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>us,
  Plus,
  Loader2,
  Check,
} from "lucide-react";
import { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";

import { cn } from "@/lib/utils";
import {
  Toolt<PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Using Lucide icons as placeholders for provider icons
import IconGroup from "@/assets/universal/icon-group.svg";
import CopySVG from "@/assets/universal/copy.svg";
import AddSVG from "@/assets/universal/add.svg";
import KeySVG from "@/assets/universal/key.svg";
import InfoSVG from "@/assets/universal/info-orange.svg";
import NextArrow from "@/assets/universal/next-arrow.svg";

import ClaudeSVG from "@/assets/universal/claude.svg";
import OpenAI from "@/assets/universal/openai.svg";
import GeminiSVG from "@/assets/universal/gemini.svg";

import InfoGray from "@/assets/universal/info-gray.svg";

import { useUniversalKey } from "@/hooks/useUniversalKey";
import { useCredits } from "@/contexts/CreditsContext";
import { Switch } from "@/components/ui/switch";
import AddActive from "@/assets/universal/add-active.svg";
import AlertBG from "@/assets/universal/alert-bg.svg";

import DeleteAndRegenerate from "@/assets/universal/delete-regenerate.svg";
import CheckSVG from "@/assets/universal/check.svg";

import SaveContinue from "@/assets/universal/save-continue.svg";
import { CustomSwitchUniversal } from "@/components/ui/custom-switch-universal";

interface Provider {
  id: string;
  name: string;
  description: string;
  icon: string;
  enabled: boolean;
}

interface UniversalModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const UniversalModal = ({
  isOpen,
  onOpenChange,
}: UniversalModalProps) => {
  // Universal Key API integration - skip initial fetch since modal starts closed
  const {
    emergentKey,
    userBudget,
    isLoadingEmergentKey,
    isLoadingUserBudget,
    isUpdatingConfig,
    isTogglingKey,
    isRegeneratingKey,
    isIncreasingBudget,
    updateKeyConfig,
    toggleKey,
    regenerateKey,
    increaseBudget,
    refetchEmergentKey,
    refetchUserBudget,
    startFetching,
  } = useUniversalKey({ skipInitialFetch: true });

  // Credit Provider integration
  const { credits, refreshCredits } = useCredits();

  // Local UI state
  const [currentView, setCurrentView] = useState<
    "main" | "addBalance" | "regenerateKey" | "confirmAutoRecharge"
  >("main");
  const [transferAmount, setTransferAmount] = useState(10.0);
  const [autoRecharge, setAutoRecharge] = useState(false);
  const [autoRechargeAmount, setAutoRechargeAmount] = useState(5.0);
  const [pendingAutoRechargeAmount, setPendingAutoRechargeAmount] = useState<
    number | null
  >(null);
  const [isCopied, setIsCopied] = useState(false);

  // Ref for debouncing auto recharge amount changes
  const autoRechargeAmountTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Ref for measuring content height
  const contentRef = useRef<HTMLDivElement>(null);

  // Initialize providers based on API data
  const [providers, setProviders] = useState<Provider[]>([
    {
      id: "claude",
      name: "Claude",
      description: "Anthropic's advanced model",
      icon: ClaudeSVG,
      enabled: true,
    },
    {
      id: "openai",
      name: "GPT",
      description: "OpenAI's advanced model",
      icon: OpenAI,
      enabled: true,
    },
    // {
    //   id: "perplexity",
    //   name: "Sonar",
    //   description: "Perplexity's fast model",
    //   icon: PerplexitySVG,
    //   enabled: true,
    // },
    {
      id: "gemini",
      name: "Gemini",
      description: "Google's advanced model",
      icon: GeminiSVG,
      enabled: false,
    },
  ]);

  // Sync providers with API data when emergentKey changes
  useEffect(() => {
    if (emergentKey?.key_config?.llm?.allowed_providers) {
      setProviders((prev) =>
        prev.map((provider) => ({
          ...provider,
          enabled: emergentKey.key_config.llm.allowed_providers.includes(
            provider.id
          ),
        }))
      );
    }
  }, [emergentKey]);

  // State to track if we're fetching data for the first time when modal opens
  const [isFetchingInitialData, setIsFetchingInitialData] = useState(false);

  // Refetch data when modal opens to ensure sync
  useEffect(() => {
    if (isOpen) {
      setIsFetchingInitialData(true);
      // Start fetching first (in case queries were skipped initially)
      startFetching();
      // Then fetch the data
      Promise.all([
        refetchEmergentKey(),
        refetchUserBudget()
      ]).finally(() => {
        setIsFetchingInitialData(false);
      });
    } else {
      // Clear pending state when modal is closed
      setPendingAutoRechargeAmount(null);
      setIsFetchingInitialData(false);
    }
  }, [isOpen, refetchEmergentKey, refetchUserBudget, startFetching]);

  // Sync auto recharge settings with API data
  useEffect(() => {
    if (emergentKey?.key_config) {
      setAutoRecharge(emergentKey.key_config.auto_topup);
      setAutoRechargeAmount(emergentKey.key_config.auto_topup_amount);
      // Clear any pending changes when syncing from API
      setPendingAutoRechargeAmount(null);
    }
  }, [emergentKey]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (autoRechargeAmountTimeoutRef.current) {
        clearTimeout(autoRechargeAmountTimeoutRef.current);
      }
    };
  }, []);

  const handleProviderToggle = async (providerId: string) => {
    // Don't allow changes when key is inactive
    if (emergentKey?.status !== "active") {
      return;
    }

    // Update local state immediately for UI responsiveness
    setProviders((prev) =>
      prev.map((provider) =>
        provider.id === providerId
          ? { ...provider, enabled: !provider.enabled }
          : provider
      )
    );

    // Update API with new configuration
    try {
      const enabledProviders = providers
        .map((p) => (p.id === providerId ? { ...p, enabled: !p.enabled } : p))
        .filter((p) => p.enabled)
        .map((p) => p.id);

      await updateKeyConfig({
        llm_providers: enabledProviders,
        auto_topup: autoRecharge,
        auto_topup_ecu: autoRechargeAmount,
      });
    } catch (error) {
      // Revert local state on error
      setProviders((prev) =>
        prev.map((provider) =>
          provider.id === providerId
            ? { ...provider, enabled: !provider.enabled }
            : provider
        )
      );
    }
  };

  const handleDeleteAndRegenerate = () => {
    setCurrentView("regenerateKey");
  };

  const handleConfirmRegenerate = async () => {
    try {
      await regenerateKey();
      setCurrentView("main");
    } catch (error) {
      console.error("Failed to regenerate key:", error);
    }
  };

  const handleAddBalance = () => {
    setCurrentView("addBalance");
  };

  const handleBackToMain = () => {
    setCurrentView("main");
    // Clear any pending changes when going back to main
    setPendingAutoRechargeAmount(null);
  };

  const handleTransferCredits = async () => {
    try {
      await increaseBudget({
        ecu: transferAmount,
        auto_topup: autoRecharge,
        auto_topup_ecu: autoRechargeAmount,
      });

      // Refresh credit provider credits after successful budget increase
      await refreshCredits();

      // After successful transfer, go back to main view
      setCurrentView("main");
    } catch (error) {
      console.error("Failed to transfer credits:", error);
    }
  };

  const handleToggleKeyStatus = async () => {
    try {
      await toggleKey({
        activate: emergentKey?.status !== "active",
      });
    } catch (error) {
      console.error("Failed to toggle key:", error);
    }
  };

  const handleAutoRechargeToggle = async (checked: boolean) => {
    // If enabling auto recharge, show confirmation modal
    if (checked) {
      setCurrentView("confirmAutoRecharge");
      return;
    }

    // If disabling, proceed directly
    try {
      // Update local state immediately for UI responsiveness
      setAutoRecharge(checked);
      // Clear any pending amount changes when disabling
      setPendingAutoRechargeAmount(null);

      // Call the same endpoint used for budget increase with 0 ECU
      await increaseBudget({
        ecu: 0,
        auto_topup: checked,
        auto_topup_ecu: autoRechargeAmount,
      });
    } catch (error) {
      // Revert local state on error
      setAutoRecharge(!checked);
      console.error("Failed to toggle auto recharge:", error);
    }
  };

  const handleConfirmAutoRecharge = async () => {
    try {
      const amountToUse =
        pendingAutoRechargeAmount !== null
          ? pendingAutoRechargeAmount
          : autoRechargeAmount;

      // Update local state immediately for UI responsiveness
      setAutoRecharge(true);
      if (pendingAutoRechargeAmount !== null) {
        setAutoRechargeAmount(pendingAutoRechargeAmount);
      }

      // Call the same endpoint used for budget increase with 0 ECU
      await increaseBudget({
        ecu: 0,
        auto_topup: true,
        auto_topup_ecu: amountToUse,
      });

      // Clear pending state and go back to main view after successful confirmation
      setPendingAutoRechargeAmount(null);
      setCurrentView("main");
    } catch (error) {
      // Revert local state on error
      setAutoRecharge(false);
      if (pendingAutoRechargeAmount !== null) {
        setAutoRechargeAmount(autoRechargeAmount); // Revert to original amount
      }
      console.error("Failed to enable auto recharge:", error);
    }
  };

  const handleAutoRechargeAmountChange = (newAmount: number) => {
    // Always set pending amount for local state tracking
    setPendingAutoRechargeAmount(newAmount);
  };

  const handleSaveAutoRechargeAmount = async () => {
    if (pendingAutoRechargeAmount === null) return;

    try {
      // Update local state
      setAutoRechargeAmount(pendingAutoRechargeAmount);

      // Call the API to save the new amount
      await increaseBudget({
        ecu: 0,
        auto_topup: autoRecharge,
        auto_topup_ecu: pendingAutoRechargeAmount,
      });

      // Clear pending state
      setPendingAutoRechargeAmount(null);
    } catch (error) {
      console.error("Failed to update auto recharge amount:", error);
      // Revert pending state on error
      setPendingAutoRechargeAmount(null);
    }
  };

  const handleCancelAutoRechargeAmount = () => {
    setPendingAutoRechargeAmount(null);
  };

  const incrementAmount = () => {
    const maxAmount = Math.min(credits || 0, 1000); // Use credit provider's available credits
    setTransferAmount((prev) => {
      const newAmount = Math.round((prev + 1) * 100) / 100; // Round to 2 decimal places
      return Math.min(newAmount, maxAmount);
    });
  };

  const decrementAmount = () => {
    setTransferAmount((prev) => {
      const newAmount = Math.round((prev - 1) * 100) / 100; // Round to 2 decimal places
      return Math.max(newAmount, 0);
    });
  };

  if (!isOpen) return null;

  // Show loading state while initial data is being fetched
  const isInitialLoading = isFetchingInitialData || (isLoadingEmergentKey && isLoadingUserBudget);

  return (
    <div
      style={{
        height: `calc(100vh - 56px)`,
        top: "56px",
        left:'0px'
      }}
      className="fixed w-full z-[49] flex items-center justify-center bg-[#0e0e0f50] backdrop-blur-[5px]"
      onClick={() => onOpenChange(false)}
    >
      {isInitialLoading ? (
        <motion.div
          className="bg-[#18181A]  rounded-[16px] p-4  md:p-8 flex flex-col items-center gap-4"
          onClick={(e) => e.stopPropagation()}
        >
          <Loader2 className="h-8 w-8 animate-spin text-[#66EAFF]" />
          <span className="font-medium text-white">
            Loading Universal Key...
          </span>
        </motion.div>
      ) : (
        <motion.div
          ref={contentRef}
          className={cn(
            "relative p-0 max-md:max-w-[95vw] bg-[#18181A] max-h-[80dvh] rounded-[16px] font-['Inter'] overflow-hidden"
          )}
          onClick={(e) => e.stopPropagation()}
          layout
          transition={{
            layout: {
              type: "spring",
              stiffness: 300,
              damping: 25,
              mass: 0.6,
            },
          }}
        >
          <AnimatePresence mode="wait">
            {currentView === "main" && (
              <motion.div
                key="main"
                className="flex relative max-md:min-w-[80vw] bg-[#18181A] md:max-w-[740px] pb-6 overflow-y-scroll max-h-[80dvh] flex-col"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                  mass: 0.5,
                  duration: 0.3,
                }}
              >
                {/* Header */}
                <div className="flex flex-col items-start p-4 pb-6 md:pt-8 md:gap-6 md:p-6">
                  <img
                    src={IconGroup}
                    alt="Icon Group"
                    className="h-10 max-md:mb-5"
                  />
                  <div className="flex items-center justify-between w-full gap-10">
                    <div className="flex flex-col items-start gap-1">
                      <h2 className="text-[18px] text-nowrap md:text-xl font-medium text-[#CCCCCC]">
                        Universal API Key
                      </h2>
                      <p className="text-[#45474D] max-md:hidden font-['Inter'] font-medium text-sm">
                        One key for all your LLM needs - no more juggling
                        multiple provider keys.
                        <br />
                        This key works automatically in all Emergent
                        applications
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <span
                        className={`min-h-[32px] max-md:hidden w-[93px] flex items-center justify-center max-h-[32px] font-['Inter'] px-5 rounded-full text-sm font-medium ${
                          emergentKey?.status === "active"
                            ? "text-[#66EAFF] bg-[#29BCCC1A]"
                            : "text-[#C8C8CA] bg-[#242426]"
                        }`}
                      >
                        {emergentKey?.status === "active"
                          ? "Active"
                          : "Inactive"}
                      </span>
                      <CustomSwitchUniversal
                        checked={emergentKey?.status === "active"}
                        onCheckedChange={handleToggleKeyStatus}
                        disabled={isTogglingKey}
                        className="min-w-14"
                      />
                    </div>
                  </div>

                  <p className="text-[#45474D] mt-1 md:hidden font-['Inter'] font-medium text-sm">
                    One key for all your LLM needs - no more juggling multiple
                    provider keys.
                    <br />
                    This key works automatically in all Emergent applications
                  </p>

                  <button
                    type="button"
                    onClick={() => onOpenChange(false)}
                    className="text-[#737380] bg-[#FFFFFF05] w-10 h-10 flex absolute right-4 top-4 md:right-6 md:top-6 items-center justify-center hover:bg-[#FFFFFF10] rounded-full hover:text-white transition-colors"
                    aria-label="Close modal"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Content */}
                <div className="flex-1 p-4 space-y-2 md:space-y-4 max-md:pt-0 md:pb-8 md:px-6">
                  {/* API Key Section */}
                  <div className="space-y-2 bg-[#1D1D1F] max-md:p-3 md:p-5 rounded-[8px]">
                    <div className="flex items-center gap-1">
                      <span className="text-[#FFFFFF]/30 text-[14px] font-medium">
                        Your Key
                      </span>
                      <TooltipProvider>
                        <Tooltip delayDuration={0}>
                          <TooltipTrigger>
                            <img
                              src={InfoGray}
                              alt="Info"
                              className="w-5 h-5"
                            />
                          </TooltipTrigger>
                          <TooltipContent className="bg-[#DDDDE6] text-black border-0">
                            Works on Emergent Apps, Keep confidential
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="flex gap-3 rounded-lg md:items-center max-md:flex-col">
                      <span className="flex-1 font-mono text-[20px] font-medium text-[#C4C4CC]">
                        {isLoadingEmergentKey ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            Loading...
                          </div>
                        ) : (
                          (() => {
                            const key = emergentKey?.emergent_key;
                            if (!key) return "No key available";

                            // Format: sk-emergent-12*****
                            if (key.startsWith("sk-emergent-")) {
                              const afterPrefix = key.substring(12); // Remove 'sk-emergent-'
                              if (afterPrefix.length > 2) {
                                return `sk-emergent-${afterPrefix.substring(
                                  0,
                                  2
                                )}${"*".repeat(5)}`;
                              }
                            }

                            // Fallback for other formats
                            if (key.length > 15) {
                              return `${key.substring(0, 15)}${"*".repeat(5)}`;
                            }

                            return key;
                          })()
                        )}
                      </span>
                      <div className="flex gap-[10px]">
                        <button
                          type="button"
                          title="Copy API Key"
                          className={cn(
                            "relative flex items-center gap-2 text-sm text-[#c4c4c4] bg-[#FFFFFF0A] hover:bg-[#FFFFFF10] p-2 rounded-[8px] hover:text-white transition-colors",
                            isCopied ? "bg-[#FFFFFF10] text-white" : ""
                          )}
                          onClick={() => {
                            if (emergentKey?.emergent_key) {
                              navigator.clipboard.writeText(
                                emergentKey.emergent_key
                              );
                              setIsCopied(true);
                              setTimeout(() => setIsCopied(false), 2000);
                            }
                          }}
                        >
                          <img
                            src={CopySVG}
                            alt="Copy"
                            className={cn(
                              "w-5 h-5 transition-opacity",
                              isCopied ? "opacity-0" : "opacity-100"
                            )}
                          />
                          <Check
                            className={cn(
                              "w-5 h-5 transition-opacity absolute",
                              isCopied ? "opacity-100" : "opacity-0"
                            )}
                          />
                        </button>
                        <button
                          type="button"
                          onClick={handleDeleteAndRegenerate}
                          disabled={isRegeneratingKey}
                          className="text-[#c4c4c4] hover:text-white max-md:justify-center max-md:w-full leading-[20px] font-medium transition-colors bg-[#FFFFFF0A] hover:bg-[#FFFFFF10] py-2 px-3 rounded-[8px] flex items-center gap-2 disabled:opacity-50"
                        >
                          {isRegeneratingKey ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : (
                            <Trash2 size={16} />
                          )}
                          Delete & Regenerate
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Balance Section */}
                  <div className="space-y-2 bg-[#1D1D1F] p-5 rounded-[8px]">
                    <div className="flex items-center gap-1">
                      <span className="text-[#FFFFFF]/30 text-[14px] font-medium">
                        API Key Balance
                      </span>
                      <TooltipProvider>
                        <Tooltip delayDuration={0}>
                          <TooltipTrigger>
                            <img
                              src={InfoGray}
                              alt="Info"
                              className="w-5 h-5"
                            />
                          </TooltipTrigger>
                          <TooltipContent className="bg-[#DDDDE6] text-black border-0">
                            Universal Key need balance to work, please add
                            accordingly
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="flex flex-col gap-3 rounded-lg md:flex-row md:items-center">
                      <span className="flex-1 font-['Inter'] font-semibold text-[24px] text-[#C4C4CC]">
                        {isLoadingUserBudget ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="w-4 h-4 animate-spin" />
                            Loading...
                          </div>
                        ) : (
                          `${userBudget?.remaining?.toFixed(2) || "0.00"} ECU`
                        )}
                      </span>
                      <div className="flex gap-[10px]">
                        <button
                          type="button"
                          onClick={handleAddBalance}
                          className="text-[#c4c4c4] max-md:w-full max-md:justify-center hover:text-[#F3CA5F] leading-[20px] font-medium transition-colors bg-[#FFFFFF0A] hover:bg-[#F3CA5F0D] py-2 px-3 rounded-[8px] flex items-center gap-2 group"
                        >
                          <img
                            src={AddSVG}
                            alt="Add"
                            className="w-6 h-6 group-hover:hidden"
                          />
                          <img
                            src={AddActive}
                            alt="Add"
                            className="hidden w-6 h-6 group-hover:block"
                          />
                          Add Balance
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Auto Recharge Section */}
                  <div className="space-y-2 bg-[#1D1D1F] p-5 rounded-[8px]">
                    <div className="flex items-center gap-1">
                      <span className="text-[#FFFFFF]/30 text-[14px] font-medium">
                        Auto Recharge
                      </span>
                      <TooltipProvider>
                        <Tooltip delayDuration={0}>
                          <TooltipTrigger>
                            <img
                              src={InfoGray}
                              alt="Info"
                              className="w-5 h-5"
                            />
                          </TooltipTrigger>
                          <TooltipContent className="bg-[#DDDDE6] text-black border-0">
                            Auto-recharge ensures your balance never runs low
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>

                    <div className="flex items-center justify-between rounded-lg max-md:gap-3">
                      <div className="flex items-center gap-3">
                        <div className="flex max-md:flex-col">
                          <div className="text-[#CCCCCC] font-['Inter'] flex max-md:flex-col max-md:items-start items-center gap-2 font-medium text-sm max-md:text-[12px]">
                            When balance hits zero, auto-recharge by
                            <div className="flex items-center gap-2">
                              <input
                                type="number"
                                value={
                                  pendingAutoRechargeAmount !== null
                                    ? pendingAutoRechargeAmount
                                    : autoRechargeAmount
                                }
                                disabled={emergentKey?.status !== "active"}
                                onChange={(e) => {
                                  const value = parseFloat(e.target.value) || 0;
                                  const maxAmount = Math.min(
                                    credits || 0,
                                    1000
                                  );
                                  const clampedValue = Math.max(
                                    0,
                                    Math.min(value, maxAmount)
                                  );
                                  const roundedValue =
                                    Math.round(clampedValue * 100) / 100;
                                  handleAutoRechargeAmountChange(roundedValue);
                                }}
                                className="w-16 px-2 py-1 bg-[#18181A] border border-[#333333] rounded-[6px] font-medium text-[#CCCCCC] focus:outline-none focus:ring-0 focus:text-white text-center [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none disabled:opacity-50 disabled:cursor-not-allowed"
                                min="0"
                                max={Math.min(credits || 0, 1000)}
                                step="0.01"
                                aria-label="Auto recharge amount"
                                title="Auto recharge amount"
                              />
                              <span className="text-[#CCCCCC] text-sm">
                                Credit
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        {pendingAutoRechargeAmount !== null && autoRecharge ? (
                          <div className="flex items-center gap-2">
                            <button
                              type="button"
                              onClick={handleSaveAutoRechargeAmount}
                              disabled={isIncreasingBudget}
                              className="flex items-center font-semibold  justify-center gap-1 p-2 text-[#0F0F10] bg-[#5FE55C] rounded-[8px] hover:bg-[#5FE55C]/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                              {isIncreasingBudget ? (
                                <>
                                  <Loader2 className="w-5 h-5 font-bold animate-spin" />
                                  Saving...
                                </>
                              ) : (
                                <>
                                  <img
                                    src={CheckSVG}
                                    alt="Check"
                                    className="w-5 h-5"
                                  />
                                  Save & Confirm
                                </>
                              )}
                            </button>
                          </div>
                        ) : (
                          <>
                            <span className="text-[#646466] font-medium">
                              {autoRecharge ? "Enabled" : "Disabled"}
                            </span>
                            <Switch
                              checked={autoRecharge}
                              onCheckedChange={handleAutoRechargeToggle}
                              disabled={
                                isUpdatingConfig ||
                                emergentKey?.status !== "active"
                              }
                              className="data-[state=checked]:bg-[#00A1B2] data-[state=unchecked]:bg-[#323233] disabled:opacity-50"
                            />
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Active Providers Section */}
                <div className="px-3 space-y-3 md:space-y-4 md:px-6">
                  <div className="flex flex-col items-start gap-1">
                    <h3 className="text-lg font-medium text-white">
                      Active Providers
                    </h3>
                    <p className="text-[#45474D] text-sm font-['Inter'] font-medium">
                      Select which AI models this key can access. You only pay
                      for what you use.
                    </p>
                  </div>

                  <div className="space-y-3 md:space-y-6 p-2 md:px-5 md:py-6 bg-[#1D1D1F] rounded-[12px]">
                    {providers.map((provider) => (
                      <div
                        key={provider.id}
                        className="flex items-center justify-between rounded-lg max-md:gap-3"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-[#242424] flex items-center justify-center">
                            <img
                              src={provider.icon}
                              alt={provider.name}
                              className="h-5 "
                            />
                          </div>
                          <div>
                            <div className="font-medium text-[#E6E6E6]">
                              {provider.name}
                            </div>
                            <div className="text-[#45474D] font-['Inter'] font-medium text-sm max-md:text-[12px]">
                              {provider.description}
                            </div>
                          </div>
                        </div>
                        <Switch
                          checked={provider.enabled}
                          onCheckedChange={() =>
                            handleProviderToggle(provider.id)
                          }
                          disabled={emergentKey?.status !== "active"}
                          className="data-[state=checked]:bg-[#00A1B2] data-[state=unchecked]:bg-[#323233] disabled:opacity-50"
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </motion.div>
            )}
            {currentView === "addBalance" && (
              <motion.div
                key="addBalance"
                className="flex relative max-md:min-w-[80vw] bg-[#18181A] md:max-w-[600px] overflow-y-scroll max-h-[80dvh] flex-col"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                  mass: 0.5,
                  duration: 0.3,
                }}
              >
                {/* Header */}
                <div className="sticky top-0 flex items-center justify-between p-4 md:p-6 md:pb-8 bg-[#18181A] z-[10]">
                  <div className="flex items-center gap-4">
                    <button
                      type="button"
                      onClick={handleBackToMain}
                      className="text-[#737380] rounded-full bg-[#FFFFFF0A] p-2  hover:text-white transition-colors"
                      aria-label="Go back"
                    >
                      <ArrowLeft size={20} />
                    </button>
                    <h2 className="text-[18px] md:text-[22px] font-medium text-white/80">
                      Add Balance
                    </h2>
                  </div>
                  <button
                    type="button"
                    onClick={() => onOpenChange(false)}
                    className="text-[#737380] bg-[#FFFFFF05] w-10 h-10 flex items-center justify-center hover:bg-[#FFFFFF10] rounded-full hover:text-white transition-colors"
                    aria-label="Close modal"
                  >
                    <X size={20} />
                  </button>
                </div>

                {/* Content */}
                <div className="flex-1 px-4 pb-4 space-y-8 md:px-6 md:pb-6">
                  {/* Current Balance Display */}
                  <div className="p-4 md:p-6 px-4 rounded-[8px] space-y-3 text-center bg-[#1D1D1F]">
                    <div className="flex items-center justify-center gap-2 text-[#737380]">
                      <img
                        src={KeySVG}
                        alt="Key"
                        className="w-6 h-6 opacity-30"
                      />
                      <span className="text-sm font-medium">
                        API Key balance
                      </span>
                    </div>
                    <div className="text-[24px] md:text-4xl font-bold text-[#C8C8CA]">
                      {isLoadingUserBudget ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="w-6 h-6 animate-spin" />
                          Loading...
                        </div>
                      ) : (
                        `${userBudget?.remaining?.toFixed(2) || "0.00"} Credits`
                      )}
                    </div>
                  </div>

                  {/* Transfer Section */}
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between max-md:gap-3">
                        <h3 className="text-[16px] font-medium text-white/80 max-md:hidden">
                          Add balance to your API Key
                        </h3>
                        <span className="text-[#666666] text-sm">
                          Available Credits:{" "}
                          <span className="font-medium text-white">
                            {credits?.toFixed(2) || "0.00"} ECU
                          </span>
                        </span>
                      </div>

                      {/* Amount Input */}
                      <div className="flex items-center bg-[#66F0FF03] border border-[#66F0FF1F] rounded-lg p-2 md:p-4">
                        <button
                          type="button"
                          onClick={decrementAmount}
                          className="text-[#66EAFF] bg-[#66F0FF1A] p-3 rounded-[8px] hover:text-[#66EAFF]/80 transition-colors"
                          aria-label="Decrease amount"
                          title="Decrease amount"
                        >
                          <Minus size={16} strokeWidth={3} />
                        </button>
                        <div className="flex-1 px-4">
                          <input
                            type="number"
                            value={transferAmount}
                            onChange={(e) => {
                              const value = parseFloat(e.target.value) || 0;
                              const maxAmount = Math.min(credits || 0, 1000); // Reasonable max limit
                              const clampedValue = Math.max(
                                0,
                                Math.min(value, maxAmount)
                              );
                              const roundedValue =
                                Math.round(clampedValue * 100) / 100; // Round to 2 decimal places
                              setTransferAmount(roundedValue);
                            }}
                            className="w-full text-[24px] font-bold text-[#C8C8CA] bg-transparent text-center border-none outline-none [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none"
                            min="0"
                            max={Math.min(credits || 0, 1000)}
                            step="0.01"
                            aria-label="Transfer amount"
                            title="Enter amount to transfer"
                          />
                        </div>
                        <button
                          type="button"
                          onClick={incrementAmount}
                          className="text-[#66EAFF] bg-[#66F0FF1A] p-3 rounded-[8px] hover:text-[#66EAFF]/80 transition-colors"
                          aria-label="Increase amount"
                          title="Increase amount"
                        >
                          <Plus size={16} strokeWidth={3} />
                        </button>
                      </div>
                    </div>

                    {/* Warning Message */}
                    <div className="flex items-start gap-2 p-3 bg-[#FFAE6614] rounded-[8px]">
                      <img
                        title="Credits transferred to your API key cannot be moved back to your account balance"
                        src={InfoSVG}
                        alt="Info"
                        className="w-6 h-6"
                      />
                      <p className="text-[#FFAE66] max-md:text-[12px] text-sm font-medium font-['Inter']">
                        Credits transferred to your API key cannot be moved back
                        to your account balance
                      </p>
                    </div>
                  </div>
                </div>

                <div className="flex sticky bottom-0 justify-end w-full bg-[#18181A] border-t border-[#242424] p-6">
                  <button
                    type="button"
                    onClick={handleTransferCredits}
                    disabled={isIncreasingBudget || transferAmount === 0}
                    className="flex items-center justify-center gap-2 px-4 py-3 font-semibold text-black bg-white rounded-full hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isIncreasingBudget ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        Transfer {transferAmount} Credits
                        <img src={NextArrow} alt="Arrow" className="w-6 h-6" />
                      </>
                    )}
                  </button>
                </div>
              </motion.div>
            )}
            {currentView === "regenerateKey" && (
              <motion.div
                key="regenerateKey"
                className="flex relative max-md:min-w-[80vw] bg-[#18181A] md:min-w-[600px] md:max-w-[600px] overflow-y-scroll max-h-[80dvh] flex-col"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                  mass: 0.5,
                  duration: 0.3,
                }}
              >
                {/* Content */}
                <div className="flex-1 px-4 pt-8 pb-4 space-y-8 md:px-8 md:pb-6">
                  {/* Warning Icon and Title */}
                  <div className="flex flex-col space-y-6 text-center">
                    <div className=" flex items-center justify-center max-h-[40px] max-w-[40px] min-h-10 min-w-10 rounded-full bg-[#FF80801A]">
                      <img src={AlertBG} alt="Alert" className="w-5 h-5" />
                    </div>

                    <div className="space-y-4">
                      <h3 className="text-[18px] md:text-[22px] font-medium text-white text-start">
                        Regenerate API Key?
                      </h3>

                      <p className="text-[#C4C4CC] max-md:text-[14px] font-['Inter'] font-medium text-start">
                        Your current key will be{" "}
                        <span className="text-[#FF6B6B] font-medium">
                          permanently deleted
                        </span>{" "}
                        and stop working immediately. Update the new key in all
                        your applications to restore functionality.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex sticky bottom-0 justify-end w-full bg-[#18181A] border-t border-[#242424] p-6 gap-4">
                  <button
                    type="button"
                    onClick={handleBackToMain}
                    className=" px-4 py-3 font-medium text-[#ACACB2] bg-[#FFFFFF0A] rounded-full hover:bg-[#FFFFFF10] hover:text-white transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleConfirmRegenerate}
                    disabled={isRegeneratingKey}
                    className=" flex items-center justify-center text-nowrap gap-2 px-4 py-3 font-semibold text-[#331A1A] bg-[#FF8080] rounded-full hover:bg-[#FF5555] disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isRegeneratingKey ? (
                      <>
                        Regenerating...
                        <Loader2 className="w-4 h-4 animate-spin" />
                      </>
                    ) : (
                      <>
                        Regenerate & Copy
                        <img
                          src={DeleteAndRegenerate}
                          alt="Arrow"
                          className="w-6 h-6"
                        />
                      </>
                    )}
                  </button>
                </div>
              </motion.div>
            )}
            {currentView === "confirmAutoRecharge" && (
              <motion.div
                key="confirmAutoRecharge"
                className="flex relative max-md:min-w-[80vw] bg-[#18181A] md:min-w-[600px] md:max-w-[600px] overflow-y-scroll max-h-[80dvh] flex-col"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                  mass: 0.5,
                  duration: 0.3,
                }}
              >
                {/* Content */}
                <div className="flex-1 px-4 pt-4 pb-4 space-y-8 md:pt-8 md:px-8 md:pb-6">
                  {/* Warning Icon and Title */}
                  <div className="flex flex-col space-y-6 text-center">
                    <img src={SaveContinue} alt="Alert" className="w-10 h-10" />

                    <div className="space-y-4">
                      <h3 className="text-[18px] md:text-[22px] font-medium text-white text-start">
                        Enable Auto recharge ?
                      </h3>

                      <p className="text-[#C4C4CC] max-md:text-[14px] font-['Inter'] font-medium text-start">
                        You're about to enable automatic recharging for your API
                        key.
                        <br /> Your API key will automatically recharge with{" "}
                        <span className="text-[#66EAFF] font-medium">
                          {(pendingAutoRechargeAmount !== null
                            ? pendingAutoRechargeAmount
                            : autoRechargeAmount
                          ).toFixed(2)}{" "}
                          credits
                        </span>{" "}
                        when the balance hits zero.
                      </p>
                    </div>
                  </div>

                  {/* Warning Box */}
                  <div className="bg-[#FFAE6614] rounded-[8px] p-4">
                    <div className="flex flex-col items-start gap-[6px]">
                      <div className="flex items-center gap-2">
                        <img src={InfoSVG} alt="Info" className="w-6 h-6" />
                        <h4 className="text-[#FFAE66] font-medium text-sm">
                          This is a one-way transfer
                        </h4>
                      </div>
                      <p className="text-[#FFAE66]/50 text-sm font-['Inter'] font-medium">
                        Credits cannot be transferred back once moved to your
                        API key. Only enable if you plan to use these credits
                        for API calls.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex sticky bottom-0 justify-end w-full bg-[#18181A] border-t border-[#242424] p-6 gap-4">
                  <button
                    type="button"
                    onClick={handleBackToMain}
                    className=" px-4 py-3 font-medium text-[#ACACB2] bg-[#FFFFFF0A] rounded-full hover:bg-[#FFFFFF10] hover:text-white transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={handleConfirmAutoRecharge}
                    disabled={isIncreasingBudget}
                    className=" flex items-center justify-center gap-2 px-4 py-3 font-semibold text-[#0F0F10] bg-[#FFFFFF] rounded-full hover:bg-[#FFFFFF]/90 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isIncreasingBudget ? (
                      <>
                        Enabling...
                        <Loader2 className="w-4 h-4 animate-spin" />
                      </>
                    ) : (
                      <>Enable Auto Recharge</>
                    )}
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      )}
    </div>
  );
};
