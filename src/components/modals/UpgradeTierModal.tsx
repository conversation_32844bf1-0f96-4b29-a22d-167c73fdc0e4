import React, { useEffect, useState } from "react";
import { createPortal } from "react-dom";
import { X, Loader2, CheckIcon } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { agentApi } from "@/services/agentApi";
import { SubscriptionPlan } from "@/store/api/apiSlice";
import { useCredits } from "@/contexts";
import { useProModePayment } from "@/hooks/useProModePayment";
import CommentWithStar from "../icons/CommentWithStar";
import NormalRobot from "../icons/Robots/NormalRobot";
import Mobile from "../icons/Mobile";
import DBIcon from "../icons/DBIcon";
import CoinIcon from "../icons/CoinIcon";
import SupportIcon from "../icons/SupportIcon";
import SparkleIcon from "../icons/SparkleIcon";
import Stars from "../icons/Stars";
import { URL_LINKS } from "@/constants/constants";

interface UpgradeTierModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

export const buttonStyle = {
  background: `
      linear-gradient(90deg, #ffffff30, #00000050),
      #F3CA5F
    `,
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundBlendMode: "overlay, normal",
  boxShadow: `
      0px 0px 20px 0px #F3CA5F66,
      0px 2.18px 3.64px 1.45px #FFFFFF66 inset,
      0px -1.45px 0px 0px #00000033 inset,
      0px 0.73px 0.73px 0.73px #0000001A
    `,
};

export const plainButtonStyle = {
  background: `#fff`,
  backgroundSize: "cover",
  backgroundPosition: "center",
  backgroundBlendMode: "overlay, normal",
};

export function UpgradeTierModal({
  isOpen,
  onOpenChange,
}: UpgradeTierModalProps) {
  const { toast } = useToast();
  const [loadingPlanId, setLoadingPlanId] = useState<string | null>(null);

  const { subscriptionPlansResponse } = useCredits();
  const { paidBefore30July, isProModeUser } = useProModePayment();

  const [showFreeSubscription, setShowFreeSubscription] = useState(true);


  useEffect(() => {
    if (isProModeUser && paidBefore30July) {
      setShowFreeSubscription(false);
    }
  }, [isProModeUser, paidBefore30July]);

  // Generic upgrade handler for any plan
  const handleUpgradeToSpecificPlan = async (plan: SubscriptionPlan) => {
    // Handle free tier downgrade
    if (plan.id === "free-tier") {
      // For free tier, we need to cancel the current subscription
      // This would typically involve calling a cancel subscription API
      toast({
        title: "Downgrade to Free",
        description: "Please contact support to downgrade to the free tier.",
        variant: "default",
      });
      return;
    }

    setLoadingPlanId(plan.id);
    try {
      const { url } = await agentApi.createCheckoutSession(0, true, plan.id);
      if (url) {
        window.location.href = url;
      } else {
        toast({
          title: "Error",
          description: "Failed to create checkout session",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Failed to create checkout session:", error);
      toast({
        title: "Error",
        description: "Failed to create checkout session",
        variant: "destructive",
      });
    } finally {
      setLoadingPlanId(null);
    }
  };

  // Helper function to get tier display name
  const getTierDisplayName = (tierName: string): string => {
    const name = tierName.replaceAll(" ", "").toLowerCase();
    if (name.includes("standard")) return "Standard";
    if (name.includes("starter")) return "Starter";
    if (name.includes("pro")) return "Pro";
    return tierName;
  };

  // Helper function to determine if a plan is an upgrade or downgrade
  const getChangeType = (plan: SubscriptionPlan): "upgrade" | "downgrade" => {
    if (!currentSubscription) return "upgrade";
    return plan.amount > currentSubscription.amount ? "upgrade" : "downgrade";
  };

  // Create a free tier plan object
  const freeTierPlan: SubscriptionPlan = {
    id: "free-tier",
    tier_name: "Free",
    amount: 0,
    ecu_credits: 5,
    billing_period: "MONTHLY" as const,
    currency: "USD",
  };

  // Get available plans excluding current subscription, but always include free tier
  const availablePlans = [
    // Always include free tier unless user is already on free tier
    ...(subscriptionPlansResponse?.current_subscription ? [freeTierPlan] : []),
    // Include other plans excluding current subscription
    ...(subscriptionPlansResponse?.available_plans?.filter(
      (plan) =>
        plan.tier_name.toLowerCase() !==
          subscriptionPlansResponse?.current_subscription?.tier_name.toLowerCase() &&
        (plan.tier_name.toLowerCase() !== "emergent pro" || paidBefore30July)
    ) || []),
  ].filter(
    (plan, index, self) =>
      // Remove duplicates and don't show free tier if user is already on free tier
      self.findIndex((p) => p.id === plan.id) === index &&
      !(
        plan.id === "free-tier" &&
        !subscriptionPlansResponse?.current_subscription
      )
  );

  // Get current subscription for display
  const currentSubscription = subscriptionPlansResponse?.current_subscription;

  const ProPlanFeatures = () => {
    return (
      <div className="flex-grow space-y-2 md:space-y-4">
        <div className="flex items-start">
          <CommentWithStar color="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="font-medium text-[#CCC]">System Prompt Edit</span>
          </span>
        </div>
        <div className="flex items-start">
          <NormalRobot primaryColor="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-[#CCC]">Ability to build Custom Agents</span>
          </span>
        </div>
        <div className="flex items-start">
          <Mobile color="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-[#CCC]">Mobile App Development</span>
          </span>
        </div>
        <div className="flex items-start">
          <DBIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-[#CCC]">2x Bigger Machine</span>
          </span>
        </div>
        <div className="flex items-start">
          <CoinIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-[#CCC]">750 Monthly Credits</span>
          </span>
        </div>
        <div className="flex items-start">
          <SupportIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-[#CCC]">Priority Support</span>
          </span>
        </div>
      </div>
    );
  };

  const StandardPlanFeatures = ({ plan }: { plan: SubscriptionPlan }) => {
    return (
      <div className="flex-grow space-y-2 md:space-y-4">
        <div className="flex items-start">
          <CoinIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="font-medium">{plan.ecu_credits} credits</span>
            <span className="text-gray-400"> per month</span>
          </span>
        </div>
        <div className="flex items-start">
          <SparkleIcon fill="#ccc" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-gray-400">
              Ability to buy additional credits
            </span>
          </span>
        </div>
      </div>
    );
  };

  const FreeTierFeatures = () => {
    return (
      <div className="flex-grow space-y-2 md:space-y-4">
        {/* <div className="flex items-start">
          <CoinIcon fill="#CCC" className="w-5 h-5 mr-2 mt-0.5" />
          <span className="text-base">
            <span className="font-medium">5 credits</span>
            <span className="text-gray-400"> per month</span>
          </span>
        </div> */}
        <div className="flex items-start">
          <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-gray-400">Basic access to all features</span>
          </span>
        </div>
        {/* <div className="flex items-start">
          <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
          <span className="text-base">
            <span className="text-gray-400">Community support</span>
          </span>
        </div> */}
      </div>
    );
  };

  if (!isOpen) return null;

  return createPortal(
    <AnimatePresence>
      <div className="fixed inset-0 z-[999] flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="absolute inset-0 bg-black/80 backdrop-blur-sm"
          onClick={() => onOpenChange(false)}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          transition={{ duration: 0.2, ease: "easeOut" }}
          className="relative max-w-[95%] md:p-[24px] md:px-[0px] w-fit flex flex-col items-center max-h-[90dvh] bg-[#18181A] rounded-2xl shadow-2xl overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
           {/* Header */}
          <div className="flex items-center justify-center w-full p-4 md:p-6">
            <div className="flex flex-col items-center justify-center gap-1">
              <h2 className="text-[20px] md:text-[26px] font-medium text-[#E6E6E6]">
                Choose your plan
              </h2>
              <span className="text-[#FFFFFF]/40 text-center max-md:text-[12px] max-w-[90%] font-inter font-medium">
                Pick a plan that best suits you. Upgrade or downgrade at any
                time.
              </span>
            </div>
            <button
              type="button"
              onClick={() => onOpenChange(false)}
              disabled={loadingPlanId !== null}
              className={cn(
                "p-2 bg-[#FFFFFF05] absolute top-4 right-4 backdrop-blur-lg hover:bg-[#242424] rounded-full transition-colors",
                loadingPlanId !== null && "opacity-50 cursor-not-allowed"
              )}
              aria-label="Close modal"
            >
              <X className="w-5 h-5 text-[#737780]" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 w-full pb-6 overflow-y-auto md:px-8">
            <div className={cn(
              // Base mobile styles
              "flex min-h-0 gap-4 overflow-x-auto flex-nowrap",
              // Base desktop styles
              "md:grid md:gap-6",
              // Dynamic grid columns based on plan count and free subscription visibility
              {
                "md:grid-cols-3": (availablePlans.length === 2 && showFreeSubscription) || availablePlans.length === 3,
                "md:grid-cols-2": (availablePlans.length === 2 && !showFreeSubscription) || availablePlans.length === 1,
              }
            )}>
              {/* Current Subscription - Always show current tier */}
             {showFreeSubscription &&  <div className="bg-[#1C1C1F] max-md:ml-4 rounded-xl p-3 pb-6 md:p-6 flex flex-col transition-colors duration-200 w-[300px] min-h-[350px] md:w-full md:min-w-0 md:max-w-none flex-shrink-0">
                <h3 className="mb-2 text-[16px] font-semibold text-[#E6E6E6] font-['Inter']">
                  {currentSubscription
                    ? getTierDisplayName(currentSubscription.tier_name)
                    : "Free"}{" "}
                  Tier
                </h3>
                <div className="flex items-baseline mb-6">
                  <span className="text-[24px] md:text-[32px] text-[#E6E6E6] font-brockmann font-bold">
                    ${currentSubscription?.amount || 0}
                  </span>
                  <span className="ml-2 text-gray-400">/ month</span>
                </div>

                <button
                  type="button"
                  className="w-full mb-6 md:mb-6 p-[10px] pr-[16px] text-base font-semibold rounded-full items-center flex justify-center bg-white/15 text-white border-[#3A3A3B] hover:bg-[#3A3A3B]"
                >
                  Current Active Plan
                </button>

                <div className="flex-grow space-y-4">
                  <div className="flex items-start">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-base">
                      <span className="font-medium">
                        {currentSubscription?.ecu_credits || 5} credits
                      </span>
                      <span className="text-gray-400"> per month</span>
                    </span>
                  </div>
                  <div className="flex items-start">
                    <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                    <span className="text-base">
                      <span className="text-gray-400">
                        {currentSubscription
                          ? "Premium access"
                          : "Standard access"}
                      </span>
                    </span>
                  </div>
                  {currentSubscription && (
                    <>
                      <div className="flex items-start">
                        <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                        <span className="text-base">
                          <span className="text-gray-400">
                            Early access to new features
                          </span>
                        </span>
                      </div>
                      <div className="flex items-start">
                        <CheckIcon className="w-5 h-5 text-white mr-2 mt-0.5" />
                        <span className="text-base">
                          <span className="text-gray-400">
                            Priority service during peak hours
                          </span>
                        </span>
                      </div>
                    </>
                  )}
                </div>
              </div>}

              {/* Available Upgrade Plans */}
              {availablePlans.map((plan, index) => (
                <div
                  key={plan.id}
                  className={cn("bg-[#1C1C1F] rounded-xl p-3 pb-6 md:p-6 flex flex-col relative transition-colors duration-200  w-[340px] min-h-[350px] max-md:max-w-[300px] md:min-w-0 md:max-w-none flex-shrink-0", index === availablePlans.length - 1 && "max-md:mr-4")}
                >
                  <h3
                    className={cn(
                      "text-base gap-1 mb-2 text-[#808080] font-semibold flex items-center font-['Inter']",
                      getTierDisplayName(plan.tier_name) === "Pro" &&
                        "text-[#F3CA5F]"
                    )}
                  >
                    {getTierDisplayName(plan.tier_name)}{" "}
                    {getTierDisplayName(plan.tier_name) === "Pro" && (
                      <Stars color="#F3CA5F" size={24} height={24} width={24} />
                    )}
                  </h3>
                  <div className="flex items-baseline mb-6">
                    <span className="text-[24px] font-bold md:text-4xl">
                      ${plan.amount}
                    </span>
                    <span className="ml-2 text-gray-400">/ month</span>
                  </div>

                  <button
                    type="button"
                    style={
                      getTierDisplayName(plan.tier_name) === "Pro"
                        ? buttonStyle
                        : plainButtonStyle
                    }
                    className="w-full mb-6 md:mb-6 p-[10px] pr-[16px] rounded-full font-brockmann font-semibold text-base text-black tracking-[-0.2px]"
                    onClick={() => handleUpgradeToSpecificPlan(plan)}
                    disabled={loadingPlanId === plan.id}
                  >
                    {loadingPlanId === plan.id ? (
                      <div className="flex items-center">
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        <span>Processing...</span>
                      </div>
                    ) : plan.id === "free-tier" ? (
                      `Downgrade to ${getTierDisplayName(plan.tier_name)}`
                    ) : (
                      `${
                        getChangeType(plan) === "upgrade"
                          ? "Upgrade"
                          : "Downgrade"
                      } to ${getTierDisplayName(plan.tier_name)}`
                    )}
                  </button>

                  {getTierDisplayName(plan.tier_name) === "Pro" && (
                    <ProPlanFeatures />
                  )}
                  {getTierDisplayName(plan.tier_name) === "Standard" && (
                    <StandardPlanFeatures plan={plan} />
                  )}
                  {getTierDisplayName(plan.tier_name) === "Starter" && (
                    <StandardPlanFeatures plan={plan} />
                  )}
                  {getTierDisplayName(plan.tier_name) === "Free" && (
                    <FreeTierFeatures />
                  )}
                </div>
              ))}
            </div>

            {/* Help section */}
            <div className="flex justify-center pt-4 md:pt-8">
              <span className="text-[#FFFFFF66] font-inter">
                Need help?{" "}
                <a
                  href={URL_LINKS.support}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-[#FFFFFF] font-inter"
                >
                  Contact support
                </a>
              </span>
            </div>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>,
    document.body
  );
}
