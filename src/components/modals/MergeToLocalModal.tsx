import React from 'react';
import { Loader2 } from "lucide-react";
import { Check } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "../ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import PlayIcon from "@/assets/play.svg";
import mergeSteps from "@/data/mergeSteps.json";
import type { MergeSteps, Step, Command } from "@/types/mergeSteps";
// @ts-ignore
import AnimatedSpinner from "@/assets/animated-spinner.gif";
import SuccessFolder from "@/assets/success-folder.svg";
import RightArrowIcon from "@/assets/right-arrow.svg";
import InactiveFolderIcon from "@/assets/inactive-folder.svg";
import "@/styles/scrollbar.css";
import { cn } from "@/lib/utils";

const typedMergeSteps = mergeSteps as MergeSteps;

interface StepCommandProps {
  command: Command;
}

const StepCommand: React.FC<StepCommandProps> = ({ command }) => {
  const renderCommandWithQuotes = (text: string) => {
    const parts = text.split(/(\"[^"]*\")/g);
    return parts.map((part, index) => {
      if (part.startsWith('"') && part.endsWith('"')) {
        return <span key={index} className="text-[#29CC83] font-berkeley">{part}</span>;
      }
      return <span key={index} className='font-berkeley'>{part}</span>;
    });
  };

  return (
    <div className="bg-[#18181A] rounded-lg px-4 py-3">
      <div className="font-berkeley text-[13px] leading-5 text-[#A3A3A3] break-all">
        {renderCommandWithQuotes(command.command)}
      </div>
    </div>
  );
};

interface StepItemProps {
  step: Step;
  status: 'completed' | 'active' | 'pending';
  currentCommandIndex?: number;
}

const StepItem: React.FC<StepItemProps> = ({ step, status, currentCommandIndex = 0 }) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        {status === 'completed' ? (
          <span className="text-step-success">
            <Check className="w-4 h-4" />
          </span>
        ) : status === 'active' ? (
          <span className="text-[#43BDE3]">
            <img alt='loading' src={AnimatedSpinner} className="w-4 h-4 animate-spin" />
          </span>
        ) : (
          <span className={cn(
            "font-berkeley text-[15px] leading-6",
            "text-step-inactive/50"
          )}>
            Step {step.id}
          </span>
        )}
        <span className={cn(
          "text-[15px] leading-6 font-['Inter']",
          status === 'completed' ? "text-[#DDDDE6]" : 
          status === 'active' ? "text-[#DDDDE6]" : 
          "text-[#737780]"
        )}>
          {step.title}
        </span>
      </div>
      {status === 'active' && (
        <StepCommand command={step.commands[currentCommandIndex]} />
      )}
    </div>
  );
};

interface MergeToLocalModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading?: boolean;
  currentCommandIndex?: number;
  activeStepIndex?: number;
  isCompleted?: boolean;
}

function CompletedState() {
  return (
    <div className="flex flex-col items-start justify-center py-12">
      <div className="flex items-center gap-4 mb-6">
        <img src={SuccessFolder} alt="Success" className="w-20 h-20" />
      </div>
      <h2 className="text-[32px] font-semibold text-white mb-3">
        Successfully Merged to Your Local
      </h2>
      <p className="text-[15px] text-[#8A8B91] text-start max-w-[600px] mb-12">
        The changes have been successfully merged into your local repository.
        You can now continue working on your project or return to the home page.
      </p>
      <div className="bg-[#1C1C1F] rounded-lg p-4 flex items-center gap-3 w-full max-w-[600px]">
        <img src={InactiveFolderIcon} alt="Home Icon" className="w-6 h-6" />
        <span><span className="text-[#DDDDE6] font-mono text-sm">/Users/<USER>/E1ectron-projects/</span>
        <span className="text-[#00E599] font-mono text-sm">agent-feature</span></span>
      </div>
    </div>
  );
}

function LoadingState({ steps, getStepStatus, currentCommandIndex }: {
  steps: Step[],
  getStepStatus: (index: number) => 'completed' | 'active' | 'pending',
  currentCommandIndex: number
}) {
  return (
    <div className="space-y-6">
      {steps.map((step, index) => (
        <StepItem
          key={step.id}
          step={step}
          status={getStepStatus(index)}
          currentCommandIndex={currentCommandIndex}
        />
      ))}
    </div>
  );
}

function InitialState({ steps }: { steps: Step[] }) {
  return (
    <>
      {steps.map((step) => (
        <div key={step.id} className="space-y-4">
          <div className="flex items-center gap-2">
            <span className="text-[#737780] text-[13px] leading-5 font-medium font-berkeley">Step {step.id}</span>
            <span className="text-[#DDDDE6] text-[15px] leading-5 font-medium font-['Inter']">{step.title}</span>
          </div>
          <div className="space-y-2">
            {step.commands.map((cmd, index) => (
              <StepCommand key={index} command={cmd} />
            ))}
          </div>
        </div>
      ))}
    </>
  );
}

export function MergeToLocalModal({
  isOpen,
  onOpenChange,
  onConfirm,
  isLoading = false,
  currentCommandIndex = 0,
  activeStepIndex = 1,
  isCompleted = false,
}: MergeToLocalModalProps) {
  const getStepStatus = (index: number): 'completed' | 'active' | 'pending' => {
    if (index < activeStepIndex) return 'completed';
    if (index === activeStepIndex) return 'active';
    return 'pending';
  };

  const renderContent = () => {
    if (isCompleted) return <CompletedState />;
    if (isLoading) return (
      <LoadingState
        steps={typedMergeSteps.steps}
        getStepStatus={getStepStatus}
        currentCommandIndex={currentCommandIndex}
      />
    );
    return <InitialState steps={typedMergeSteps.steps} />;
  };

  return (
    // @ts-ignore
    <Dialog open={isOpen} onOpenChange={onOpenChange} hideCloseButton>
      <DialogContent className="sm:max-w-[670px] max-h-[85vh] flex flex-col">
        {isCompleted ? null : <DialogHeader>
          <DialogTitle>Merge Changes to Local</DialogTitle>
          <DialogDescription>
            To merge these changes to your local, Neo will run the following commands in sequence. Review them carefully before confirming to proceed.
          </DialogDescription>
        </DialogHeader>}
        <div className="flex-1 min-h-0 overflow-y-auto custom-scrollbar">
          <div className="px-6 py-8 space-y-6">
            {renderContent()}
          </div>
        </div>
        <DialogFooter className="gap-2">
          <Button
            variant="secondary"
            onClick={() => onOpenChange(false)}
          >
            {isCompleted ? "Close" : "Cancel"}
          </Button>
          {isLoading ? (
            <Button
              onClick={onConfirm}
              disabled={isLoading}
              size="default"
              className="font-semibold text-[#43BDE3] bg-[#122024]"
              rightIcon={<img alt='loading' src={AnimatedSpinner} className="w-6 h-6" />}
            >
              Executing...
            </Button>
          ) : isCompleted ? (
            <Button
              onClick={onConfirm}
              size="default"
              className="font-semibold bg-white"
              rightIcon={<img alt='right-arrow' src={RightArrowIcon} className="w-5 h-5" />}
            >
              Go to Home
            </Button>
          ) : (
            <Button
              onClick={onConfirm}
              disabled={isLoading}
              size="default"
              className="font-semibold"
              rightIcon={<img alt='play' src={PlayIcon} className="w-6 h-6" />}
            >
              Confirm and Execute
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
