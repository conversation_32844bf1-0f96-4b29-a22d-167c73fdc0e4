/**
 * Represents a base64-encoded image for API transmission
 */
export interface Base64Image {
    mime_type: string;
    img_base64: string;
}

/**
 * File handling strategy types
 */
export type FileHandlingStrategyType = "artifact_only" | "dual_mode" | "compress_and_dual";

/**
 * File handling strategy configuration
 */
export interface FileHandlingStrategy {
    strategy: FileHandlingStrategyType;
    needsCompression: boolean;
    canSendToAgent: boolean;
    supportsDualMode: boolean;
}

/**
 * Result of compressing an image
 */
export interface CompressibleImageResult {
    shouldUseBase64: boolean;
    base64Data?: string;
    compressedSize?: number;
}

/**
 * Result of processing files for dual mode (artifacts + base64)
 */
export interface DualModeProcessingResult {
    base64Images: Base64Image[];
    filesToUploadAsArtifacts: File[];
    compressedImageData: Map<string, CompressibleImageResult>;
}

/**
 * Progress callback for file processing
 */
export type FileProcessingProgressCallback = (current: number, total: number) => void;

/**
 * Supported compressible image MIME types
 */
export const COMPRESSIBLE_IMAGE_TYPES = [
    'image/jpeg',
    'image/png', 
    'image/gif',
    'image/webp'
] as const;

/**
 * Maximum file size for base64 conversion (5MB)
 */
export const MAX_BASE64_SIZE_BYTES = 5 * 1024 * 1024;

/**
 * Image compression settings
 */
export interface ImageCompressionSettings {
    maxWidth: number;
    maxHeight: number;
    quality: number; // 0-100
    format: 'jpeg' | 'png' | 'webp';
}

/**
 * Default compression settings for images
 */
export const DEFAULT_COMPRESSION_SETTINGS: ImageCompressionSettings = {
    maxWidth: 2000,
    maxHeight: 2000,
    quality: 30,
    format: 'jpeg'
};
