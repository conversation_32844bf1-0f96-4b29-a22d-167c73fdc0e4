import {
    FileHandlingStrategy,
    CompressibleImageResult,
    Base64Image,
    DualModeProcessingResult,
    FileProcessingProgressCallback,
    COMPRESSIBLE_IMAGE_TYPES,
    MAX_BASE64_SIZE_BYTES
} from '@/lib/types/fileTypes';

/**
 * Determines the handling strategy for a file based on its type
 */
export const getFileHandlingStrategy = (file: File): FileHandlingStrategy => {
    const isImage = file.type.startsWith('image/');
    const isCompressibleImg = COMPRESSIBLE_IMAGE_TYPES.includes(file.type as any);

    // Non-images: always upload as artifacts, no base64 conversion
    if (!isImage) {
        return {
            strategy: "artifact_only",
            needsCompression: false,
            canSendToAgent: false,
            supportsDualMode: false
        };
    }

    // Non-compressible images (SVG, TIFF, etc.): upload as artifacts AND try base64 if under 5MB
    if (isImage && !isCompressibleImg) {
        return {
            strategy: "dual_mode",
            needsCompression: false,
            canSendToAgent: true,
            supportsDualMode: true // Both artifact and base64 if small enough
        };
    }

    // Compressible images: compress, then use dual mode (artifact + base64 if under 5MB)
    if (isImage && isCompressibleImg) {
        return {
            strategy: "compress_and_dual",
            needsCompression: true,
            canSendToAgent: true,
            supportsDualMode: true
        };
    }

    return {
        strategy: "artifact_only",
        needsCompression: false,
        canSendToAgent: false,
        supportsDualMode: false
    };
};

/**
 * Processes a compressible image by compressing it and checking if it should use base64
 */
export const processCompressibleImage = async (
    file: File,
    compressImage: (file: File) => Promise<any>
): Promise<CompressibleImageResult> => {
    try {
        console.log('FileProcessing - Processing compressible image:', {
            fileName: file.name,
            originalSize: `${(file.size / 1024 / 1024).toFixed(2)}MB`,
            originalType: file.type
        });

        // Compress the image
        const compressionResult = await compressImage(file);
        console.log('FileProcessing - Compression result:', compressionResult);
        
        if (!compressionResult) {
            console.error('FileProcessing - Failed to compress image:', file.name);
            return { shouldUseBase64: false };
        }

        const compressedSizeMB = compressionResult.compressedSize / 1024 / 1024;
        console.log(`FileProcessing - Compressed ${file.name}:`, {
            originalSize: `${(compressionResult.originalSize / 1024 / 1024).toFixed(2)}MB`,
            compressedSize: `${compressedSizeMB.toFixed(2)}MB`,
            compressionRatio: `${compressionResult.compressionRatio.toFixed(1)}%`,
            base64Length: compressionResult.base64?.length || 0
        });

        // Check if compressed size is under 5MB limit for base64
        const shouldUseBase64 = compressionResult.compressedSize <= MAX_BASE64_SIZE_BYTES;

        if (shouldUseBase64) {
            console.log(`FileProcessing - Image ${file.name} will use base64 (${compressedSizeMB.toFixed(2)}MB)`);
            return {
                shouldUseBase64: true,
                base64Data: compressionResult.base64,
                compressedSize: compressionResult.compressedSize
            };
        } else {
            console.log(`FileProcessing - Compressed image ${file.name} is still over 5MB (${compressedSizeMB.toFixed(2)}MB), will upload as artifact`);
            return {
                shouldUseBase64: false,
                compressedSize: compressionResult.compressedSize
            };
        }
    } catch (error) {
        console.error('FileProcessing - Failed to process compressible image:', file.name, error);
        return { shouldUseBase64: false };
    }
};

/**
 * Converts a file to base64 without compression
 */
export const convertFileToBase64 = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
            const result = reader.result as string;
            const base64 = result.split(',')[1]; // Remove data URL prefix
            resolve(base64);
        };
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
};

/**
 * Processes files for dual mode (artifacts + base64)
 * Returns arrays of base64 images and files for artifact upload
 */
export const processFilesForDualMode = async (
    files: File[],
    compressImage: (file: File) => Promise<any>,
    onProgress?: FileProcessingProgressCallback
): Promise<DualModeProcessingResult> => {
    const base64Images: Base64Image[] = [];
    const filesToUploadAsArtifacts = [...files]; // ALL files will be uploaded as artifacts
    const compressedImageData = new Map<string, CompressibleImageResult>();

    console.log('FileProcessing - Processing files for dual mode:', files.length);

    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        onProgress?.(i + 1, files.length);
        
        const strategy = getFileHandlingStrategy(file);
        
        if (strategy.strategy === "compress_and_dual") {
            // Compressible images: compress and check if we can also send as base64
            console.log(`FileProcessing - Processing compressible image: ${file.name}`);
            const result = await processCompressibleImage(file, compressImage);
            console.log(`FileProcessing - Compression result for ${file.name}:`, result);
            
            // Store compression result for this file
            compressedImageData.set(file.name, result);
            
            if (result.shouldUseBase64 && result.base64Data) {
                // Compressed image is under 5MB, ALSO send as base64 (in addition to artifact)
                const base64Image = {
                    mime_type: 'image/jpeg', // Always JPEG after compression
                    img_base64: result.base64Data
                };
                base64Images.push(base64Image);
                console.log(`FileProcessing - Image ${file.name} will be BOTH artifact AND base64:`, {
                    mime_type: base64Image.mime_type,
                    base64_length: base64Image.img_base64.length,
                    compressed_size_mb: (result.compressedSize! / 1024 / 1024).toFixed(2)
                });
            } else {
                console.log(`FileProcessing - Image ${file.name} will be artifact ONLY (compressed size over 5MB)`);
            }
        } else if (strategy.strategy === "dual_mode") {
            // Non-compressible images: check if original file is under 5MB for base64
            if (file.size <= MAX_BASE64_SIZE_BYTES) {
                // Convert original file to base64 without compression
                try {
                    const base64Data = await convertFileToBase64(file);
                    const base64Image = {
                        mime_type: file.type, // Keep original MIME type
                        img_base64: base64Data
                    };
                    base64Images.push(base64Image);
                    console.log(`FileProcessing - Non-compressible image ${file.name} will be BOTH artifact AND base64:`, {
                        mime_type: base64Image.mime_type,
                        base64_length: base64Image.img_base64.length,
                        original_size_mb: (file.size / 1024 / 1024).toFixed(2)
                    });
                } catch (error) {
                    console.error(`FileProcessing - Failed to convert ${file.name} to base64:`, error);
                    console.log(`FileProcessing - Image ${file.name} will be artifact ONLY (base64 conversion failed)`);
                }
            } else {
                console.log(`FileProcessing - Non-compressible image ${file.name} will be artifact ONLY (original size over 5MB)`);
            }
        } else {
            console.log(`FileProcessing - File ${file.name} will be artifact ONLY (${strategy.strategy})`);
        }
    }

    return {
        base64Images,
        filesToUploadAsArtifacts,
        compressedImageData
    };
};
