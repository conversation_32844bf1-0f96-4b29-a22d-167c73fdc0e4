export interface Skill {
    id: string;
    name: string;
    description: string;
    short_desc?: string;
    latest_version: string;
    job_id: string;
    created_at: string;
    updated_at: string;
  }

  export interface SkillDescription {
    short_desc: string;
    description: string;
    display_name: string;
  }

  export interface SkillDescriptionItem {
    [skillName: string]: SkillDescription;
  }

  export  interface App {
    name: string;
    job_id: string;
    description: string;
    preview_url: string;
  }

  export  interface PortfolioCategory {
    apps: App[];
    category_name: string;
  }

  export interface Template {
    id: string;
    name: string;
    code_path: string;
    env_image: string;
    image_tag: string;
    description: string;
    is_cloud_flow: boolean;
    is_template_enabled: boolean;
    full_stack_supervisor_config?: boolean;
  }

  export interface ModelInfo {
    name: string;
    display_name: string;
  }

  export interface AgentInfo {
    name: string;
    sub_agents?: string[];
    "sub-agents"?: string[];
    display_name: string;
  }

  export interface ChipData {
    icon_url: string;
    category: string;
    label: string;
    prompt: string;
  }

  export interface ChipCategories {
    [categoryName: string]: ChipData[];
  }

  export  interface FormField {
    id: string;
    label: string;
    show?: boolean | string;
    isAdvanced?: boolean;
  }

  export  interface FormDefaults {
    agent: string;
    budget: number;
    baseModel: string;
    dockerImage: string;
    portMappings: any[];
    projectFolder: string;
    repo_selector: string;
    taskDescription: string;
    skills?: any[];
  }

  export  interface FormConfig {
    fields: FormField[];
    defaults: FormDefaults;
  }

  export interface FormEnvironments {
    cloud: FormConfig;
    local: FormConfig;
  }

export  interface FormLayouts {
    new_app: FormEnvironments;
    from_github: FormEnvironments;
    existing_project: FormEnvironments;
  }

export  interface FormFieldMetadata {
    id: string;
    show?: string;
    label: string;
    required: boolean;
    description: string;
  }

 export interface FormFieldsMetadata {
    agent: FormFieldMetadata;
    budget: FormFieldMetadata;
    skills: FormFieldMetadata;
    baseModel: FormFieldMetadata;
    dockerImage: FormFieldMetadata;
    portMappings: FormFieldMetadata;
    projectFolder: FormFieldMetadata;
    repo_selector: FormFieldMetadata;
    taskDescription: FormFieldMetadata;
  }

 export interface GlobalConfig {
    skills: Skill[];
    skills_description?: SkillDescriptionItem[];
    portfolio: PortfolioCategory[];
    templates: Template[];
    model_list: ModelInfo[];
    agent_names: AgentInfo[];
    form_layouts: FormLayouts;
    max_budget_limit: number;
    default_cost_limit: number;
    form_field_metadata: FormFieldsMetadata;
    open_folder_templates: Template[];
    plugin_library_version: string;
    prompt_suggestions?: ChipData[];
    overload_lock: {
      enabled: boolean;
      capacity: string;
      concurrent_tasks: string;
      locked_until: string;
      title: string;
      description: string;
    };
    e1_experimental?: {
      agent?: string;
      baseModel?: string;
      dockerImage?: string;
      budget?: number;
    };
    info_banner?: {
      show: boolean;
      content: string;
      dismissible: boolean;
    };
    lite_version?: {
      enabled: boolean;
      code: string;
    };
    feature_flags:{
      universal_key: boolean | string[];
    }
  }