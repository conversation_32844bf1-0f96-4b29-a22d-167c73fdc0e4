import { test, expect } from '@playwright/test';

test('email-password-login', async ({ page }) => {
  await page.goto('http://localhost:5173/login');
  await page.getByRole('textbox', { name: 'Enter your email' }).click();
  await page.getByRole('textbox', { name: 'Enter your email' }).fill('<EMAIL>');
  await page.getByRole('textbox', { name: 'Enter your password' }).click();
  await page.getByRole('textbox', { name: 'Enter your password' }).fill('emergent@2024');
  await page.getByRole('button', { name: 'Log In' }).click();
  await page.waitForURL('http://localhost:5173/', {
    timeout: 5000
  });
});